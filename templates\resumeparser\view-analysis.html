{% extends 'ecom/admin_base.html' %}
{% load static %}

{% block scripts %}
    <!-- Include jQ<PERSON>y and Bootstrap JS -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://stackpath.bootstrapcdn.com/bootstrap/4.3.1/js/bootstrap.min.js"></script>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.3/font/bootstrap-icons.min.css">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.7.0.min.js"></script>
    
    <script>
    
        // Store candidate data for JavaScript access
        const candidatesData = {{file_content_dump|safe}}.candidates;
        console.log(candidatesData);
        const attributesByCandidate = {{attributes_by_candidate|safe}};

        var summaryStatistics = {};

        calculateSummaryStatistics();
        


        // jQuery-based filtering functionality
        $(document).ready(function() {

            updateSummaryStatistics();
            
            const protocol = window.location.protocol === "https:" ? "wss" : "ws";
            const path = "/ws/analyse-resume/{{job_code}}/";

            websocket_url = `${protocol}://${window.location.hostname}:${window.location.port}${path}`;

            const socket = new WebSocket(websocket_url);

            var myModal = new bootstrap.Modal(document.getElementById('myModal'));


            // Ensure unique keys on form submit
            $('#dynamic-form').on('submit', function(e) {
                e.preventDefault();
                const keyInputs = $(this).find('input[name="custom_key"]');
                const valueInputs = $(this).find('input[name="custom_value"]');
                let keyValueMap = {};
                let duplicateKey = false;
                keyInputs.each(function(idx) {
                    const key = $(this).val().trim();
                    const value = valueInputs.eq(idx).val().trim();
                    if (key in keyValueMap) {
                        duplicateKey = true;
                    }
                    keyValueMap[key] = value;
                });
                if (duplicateKey) {
                    alert('Duplicate field names are not allowed. Please use unique keys.');
                    return false;
                }
                // Add candidate name to the payload
                keyValueMap['candidate_filename'] = $('#candidateIdentifierByFileName').val();
                // AJAX POST to the specified URL
                $.ajax({
                    type: 'POST',
                    url: window.location.href,
                    data: JSON.stringify(keyValueMap),
                    contentType: 'application/json',
                    success: function(response) {
                        // Optionally show a success message
                        alert('Details submitted successfully!');
                        // $('#customAttributesModal').modal('hide');
                        window.location.reload();
                    },
                    error: function(xhr, status, error) {
                        alert('Error submitting details: ' + error);
                    }
                });
                return false;
            });
            console.log('Document ready, setting up filters...');

            applySorting("Score");

            // Initialize filters
            initializeFilters();

            // Initial count update
            updateVisibleCount();

            const container = $('#dynamic-fields-container');

            // Event listener for the "Add Field" button
            $('#addFieldBtn').on('click', addNewDetailRow);

            container.on('click', '.remove-row-btn', function() {
                // Find the closest parent with the .input-group class and remove it
                $(this).closest('.input-group').remove();
            });

            // Add one row by default when the modal is shown for the first time
            $('#analysisModal').on('shown.bs.modal', function () {
                if (container.children().length === 0) {
                    addNewDetailRow();
                }
            });

            socket.onopen = (event) => {
                console.log('WebSocket connection established.');
            };

            socket.onclose = (event) => {
                console.log('WebSocket connection closed.');
            };

            socket.onmessage = (event) => {
                
                const data = JSON.parse(event.data);
                if (!data.batch_analyse_success) {
                    console.log('WebSocket data: ' , event.data);
                }

                if (data.analysis_started) {
                    // myModal.show();
                    showProgressPopup('Analysis started successfully!', 'info');
                }
                if (data.analysis_completed) {
                    // myModal.hide();

                    // Auto-hide batch completion popup after 2 seconds
                    hideProgressPopup();

                    setTimeout(() => {
                        showProgressPopup('All analysis completed! ✅', 'success');
                    }, 500);

                    // showProgressPopup('All analysis completed! ✅', 'success');

                    // Auto-hide batch completion popup after 2 seconds
                    setTimeout(() => {
                        hideProgressPopup();
                    }, 6000);
                }

                if (data.batch_analyse_success) {

                    const batchNo = data.batch_no;
                    const jobCode = data.job_code;

                    // Get total batches from cache or estimate
                    const totalBatches = data.total_batches || Math.ceil(data.total_resumes / 2) || '?';

                    const $tbody = $('#candidatesTable tbody');
                    var $rows = $tbody.find(".candidate-row").get();
                    console.log($rows);

                    if (data.results.success) {
                        const new_candidates_data = data.results.results;
                        $(new_candidates_data).each((index , candidate) => {
                            var row = `
                                <tr class="candidate-row"
                                    data-recommendation="${ candidate.recommendation.decision }"
                                    data-experience="${ candidate.experience_analysis.experience_level }"
                                    data-score="${ candidate.scores.final_score }"
                                    data-name="${ candidate.candidate_name.toLowerCase() }">
                                    <td>
                                        <div>
                                            <strong>${ candidate.candidate_name }</strong>
                                            <br>
                                            <small class="text-muted">${ candidate.filename }</small>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <div class="progress me-2" style="width: 60px; height: 8px;">
                                                <div class="progress-bar
                                                    ${
                                                        candidate.scores.final_score >= 80 ? "bg-lime-green" :
                                                        candidate.scores.final_score >= 60 ? "bg-info":
                                                        candidate.scores.final_score >= 40 ? "bg-warning":
                                                        "bg-danger"
                                                        }
                                                    text-white"
                                                    style="width: ${ candidate.scores.final_score }%"></div>
                                            </div>
                                            <span class="fw-bold">${ candidate.scores.final_score.toFixed(1) }</span>
                                        </div>
                                    </td>
                                    <td>
                                        <span class="badge
                                            ${
                                                candidate.skills_analysis.skill_match_percentage >= 70 ? "bg-lime-green" :
                                                candidate.skills_analysis.skill_match_percentage >= 50 ? "bg-info" :
                                                candidate.skills_analysis.skill_match_percentage >= 30 ? "bg-warning" :
                                                "bg-danger"
                                            } text-white">

                                            ${ candidate.skills_analysis.skill_match_percentage.toFixed(1) }%
                                        </span>
                                    </td>
                                    <td>
                                        <span class="badge

                                            ${
                                                candidate.experience_analysis.experience_level == "SENIOR" ? "bg-lime-green":
                                                candidate.experience_analysis.experience_level == "MID" ? "bg-info":
                                                "bg-warning"
                                            } text-white">

                                            ${ candidate.experience_analysis.experience_level }
                                        </span>
                                    </td>
                                    <td>

                                        ${
                                            candidate.recommendation.decision == "HIRE" ? `<span class="badge bg-lime-green text-white">${ candidate.recommendation.decision }</span>` :
                                            candidate.recommendation.decision == "CONSIDER" ? `<span class="badge bg-warning text-white">${ candidate.recommendation.decision }</span>` :
                                            `<span class="badge bg-danger text-white">${ candidate.recommendation.decision }</span>`
                                        }

                                    </td>
                                    <td>
                                        <button class="btn btn-sm btn-outline-primary me-1" onclick="showCandidateDetails('${ candidate.email }')">
                                            <i class="bi bi-eye"></i> Details
                                        </button>
                                        <button class="btn btn-sm btn-outline-primary" onclick="showCustomAttributesForm('${ candidate.candidate_name }' , '${ candidate.filename }')" title="Add">
                                            <i class="bi bi-plus"></i>
                                        </button>
                                    </td>
                                </tr>
                            `;

                            candidatesData.push(candidate);
                            console.log("new candidate data: ",candidatesData);

                            calculateSummaryStatistics();
                            updateSummaryStatistics();
                            
                            $tbody.append(row);
                            applySorting("Score");
                            $("#candidatesContainer").css({ display: "block" });
                            $("#no-analysis-found").css({ display: "none" });

                            hideProgressPopup();
                            setTimeout(() => {
                                // console.log("After 2 seconds");
                                // Show progress popup with batch numbers
                                if (batchNo != totalBatches) {
                                    showProgressPopup(`Batch ${batchNo}/${totalBatches} completed! ✅`, 'success');
                                }
                            }, 500);

                        
                        });
                    }
                }

            }
            
            socket.onerror = (error) => {
                console.error('WebSocket error: ', error);
                socket = null;
            };


            /*
            {% comment %} {% if analysis_started %}
                var myModal = new bootstrap.Modal(document.getElementById('myModal'));
                myModal.show();

                $.ajax({
                    type: "POST",
                    url: "http://127.0.0.1:8000/analyse-pdfs/",
                    data: JSON.stringify({ job_id: {{job_id}} }),
                    contentType: "application/json",
                    success: function (data) {

                        if(data.analyseSuccess) {
                        
                        setTimeout(() => {
                            myModal.hide();
                            window.location.href = window.location.pathname;
                        }, 1000);

                        }


                    },
                    error: function (err) {
                        console.error("Error:", err);
                    },
                    complete: function () {
                        // Allow next frame after completion
                    }
                });


            {% endif %} {% endcomment %}
            */


            $('#openModalBtn').click(function() {
                /*var myModal = new bootstrap.Modal(document.getElementById('myModal'));
                myModal.show();*/
                // window.location.href = window.location.pathname + "?startAnalysis=True";
                
                const data = {
                    start_analysis: true,
                    job_code: "{{job_code}}",
                    job_id: "{{job_id}}"
                };

                socket.send(JSON.stringify(data));

            });
        });
        
        // Function to show bottom-right progress popup
        function showProgressPopup(message, type = 'info') {
            const popup = $('#progressPopup');
            const alertClass = type === 'success' ? 'alert-success' :
                                type === 'warning' ? 'alert-warning' :
                                type === 'error' ? 'alert-danger' : 'alert-info';

            popup.removeClass('alert-info alert-success alert-warning alert-danger')
                    .addClass(alertClass)
                    .html(`<i class="bi bi-activity"></i> ${message}`)
                    .fadeIn();
        }

        // Function to hide progress popup
        function hideProgressPopup() {
            $('#progressPopup').fadeOut();
        }

        function calculateSummaryStatistics() {
            var summary_statistics = {};

            let successCount = 0;
            let scoreSum = 0;

            var scoreDistribution = {
                excellent: 0,
                good: 0,
                average: 0,
                below_average: 0
            };

            var recommendations = {
                HIRE: 0,
                CONSIDER: 0,
                REJECT: 0
            };

            try {
                candidatesData.forEach(data => {
                    if (data.success === true) {
                        successCount++;
                    }

                    const finalScore = data.scores?.final_score || 0;
                    scoreSum += finalScore;

                    if (finalScore >= 90) {
                        scoreDistribution.excellent++;
                    } else if (finalScore >= 80) {
                        scoreDistribution.good++;
                    } else if (finalScore >= 60) {
                        scoreDistribution.average++;
                    } else {
                        scoreDistribution.below_average++;
                        console.log(finalScore);
                    }

                    const decision = data.recommendation?.decision;
                    if (recommendations.hasOwnProperty(decision)) {
                        recommendations[decision]++;
                    }
                });

                summary_statistics.total_candidates = candidatesData.length;
                summary_statistics.successful_analyses = successCount;
                summary_statistics.average_score = (scoreSum / candidatesData.length).toFixed(2);
                summary_statistics.score_distribution = scoreDistribution;
                summary_statistics.recommendations = recommendations;

            } catch (e) {
                console.error("Unable to calculate summary statistics:", e);
            }

            summaryStatistics = summary_statistics;
        }

        function updateSummaryStatistics() {

            // summaryStatistics

            htmlSummaryContent = `
                 <!-- Summary Statistics -->
                <div class="row mb-4">
                    <div class="col-md-4">
                        <div class="card border-0 shadow-sm h-100">
                            <div class="card-body text-center">
                                <div class="display-4 text-primary mb-2">${ summaryStatistics.total_candidates }</div>
                                <h6 class="text-muted mb-0">Total Candidates</h6>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="card border-0 shadow-sm h-100">
                            <div class="card-body text-center">
                                <div class="display-4 text-success mb-2">${ summaryStatistics.successful_analyses }</div>
                                <h6 class="text-muted mb-0">Successful Analyses</h6>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="card border-0 shadow-sm h-100">
                            <div class="card-body text-center">
                                <div class="display-4 text-warning mb-2">${ summaryStatistics.average_score }</div>
                                <h6 class="text-muted mb-0">Average Score</h6>
                            </div>
                        </div>
                    </div>
                    
                </div>

                <!-- Score Distribution and Recommendations -->
                <div class="row mb-4">
                    <div class="col-md-6">
                        <div class="card border-0 shadow-sm h-100">
                            <div class="card-header bg-light border-0">
                                <h5 class="mb-0 fw-bold">📈 Score Distribution</h5>
                            </div>
                            <div class="card-body">
                                <div class="row text-center">
                                    <div class="col-6 col-md-3 mb-3">
                                        <div class="badge bg-lime-green fs-6 mb-2 text-white">${ summaryStatistics.score_distribution.excellent }</div>
                                        <div class="small text-muted">Excellent</div>
                                    </div>
                                    <div class="col-6 col-md-3 mb-3">
                                        <div class="badge bg-info fs-6 mb-2 text-white">${ summaryStatistics.score_distribution.good }</div>
                                        <div class="small text-muted">Good</div>
                                    </div>
                                    <div class="col-6 col-md-3 mb-3">
                                        <div class="badge bg-warning fs-6 mb-2 text-white">${ summaryStatistics.score_distribution.average }</div>
                                        <div class="small text-muted">Average</div>
                                    </div>
                                    <div class="col-6 col-md-3 mb-3">
                                        <div class="badge bg-danger fs-6 mb-2 text-white">${ summaryStatistics.score_distribution.below_average }</div>
                                        <div class="small text-muted">Below Avg</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="card border-0 shadow-sm h-100">
                            <div class="card-header bg-light border-0">
                                <h5 class="mb-0 fw-bold">💼 Hiring Recommendations</h5>
                            </div>
                            <div class="card-body">
                                <div class="row text-center">
                                    <div class="col-4 mb-3">
                                        <div class="badge bg-lime-green fs-6 mb-2 text-white">${ summaryStatistics.recommendations.HIRE }</div>
                                        <div class="small text-muted">Hire</div>
                                    </div>
                                    <div class="col-4 mb-3">
                                        <div class="badge bg-warning fs-6 mb-2 text-white">${ summaryStatistics.recommendations.CONSIDER }</div>
                                        <div class="small text-muted">Consider</div>
                                    </div>
                                    <div class="col-4 mb-3">
                                        <div class="badge bg-danger fs-6 mb-2 text-white">${ summaryStatistics.recommendations.REJECT }</div>
                                        <div class="small text-muted">Reject</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            `;

            $("#summaryContainer").html(htmlSummaryContent);

        }
        

        // Toggle filters panel
        function toggleFilters() {
            $('#filtersPanel').slideToggle();
        }

        // Initialize all filter event listeners
        function initializeFilters() {
            // Recommendation filter
            $('#recommendationFilter').on('change', function() {
                console.log('Recommendation filter changed:', $(this).val());
                applyFilters();
            });

            // OrderBy filter
            $('#orderByFilter').on('change', function() {
                console.log('OrderBy filter changed:', $(this).val());
                applyOrderByFilter();
            });

            // Sort order filter - Commented for later use
            /*
            $('#sortOrderFilter').on('change', function() {
                var orderBy = $('#orderByFilter').val();
                console.log('Sort order changed:', $(this).val(), 'for:', orderBy);
                if (orderBy) {
                    applySorting(orderBy);
                }
            });
            */

            // Min score filter (using event delegation for dynamically created elements)
            $(document).on('change input', '#minScoreFilter', function() {
                var value = $(this).val();
                var parsedValue = parseFloat(value) || 0;
                console.log('Min score filter changed - Raw value:', value, 'Parsed value:', parsedValue);
                applyFilters();
            });

            // Name search filter
            $('#nameFilter').on('input keyup', function() {
                console.log('Name filter changed:', $(this).val());
                applyFilters();
            });

            // Event listeners for dynamically created filter elements
            $(document).on('change', '#experienceFilter, #recommendationOrderFilter', function() {
                console.log('Dynamic filter changed:', $(this).attr('id'), $(this).val());
                applyFilters();
            });
        }

        function applyOrderByFilter() {
            var orderByFilterValue = $('#orderByFilter').val();
            var labelValue;
            var orderByFilterInput;

            if(orderByFilterValue === "") {
                labelValue = "Order By Filter";
                orderByFilterInput = `
                <select class="form-select form-select-sm">
                        <option value="">Select a Order By Filter</option>
                    </select>
                `;
            } else if(orderByFilterValue === "Score") {
                labelValue = "Min Score Filter";
                orderByFilterInput = `
                    <input type="number" class="form-control form-control-sm" id="minScoreFilter" placeholder="Enter min score" min="0" max="100">
                `;
            } else if(orderByFilterValue === "Experience") {
                labelValue = "Experience Level";
                orderByFilterInput = `
                    <select class="form-select form-select-sm" id="experienceFilter">
                        <option value="">All Levels</option>
                        <option value="SENIOR">Senior</option>
                        <option value="MID">Mid</option>
                        <option value="JUNIOR">Junior</option>
                    </select>
                `;
            } else if(orderByFilterValue === "Recommendation") {
                labelValue = "Recommendation";
                orderByFilterInput = `
                    <select class="form-select form-select-sm" id="recommendationOrderFilter">
                        <option value="">All Recommendations</option>
                        <option value="HIRE">Hire</option>
                        <option value="CONSIDER">Consider</option>
                        <option value="REJECT">Reject</option>
                    </select>
                `;
            }

            $('#orderByFilterLabel').text(labelValue);
            $('#orderByFilterInput').html(orderByFilterInput);

            console.log('Order by filter changed:', orderByFilterValue);

            // Apply sorting immediately when order by changes
            applySorting(orderByFilterValue);
        }

        // Apply sorting based on selected criteria (descending order only for now)
        function applySorting(orderBy) {
            if (!orderBy) return;

            // Ascending/Descending functionality - Commented for later use
            /*
            var sortOrder = $('#sortOrderFilter').val() || 'desc';
            var isDescending = sortOrder === 'desc';
            */
            var isDescending = true; // Always descending for now

            var $tbody = $('#candidatesTable tbody');
            var $rows = $tbody.find('.candidate-row').get();

            $rows.sort(function(a, b) {
                var aVal, bVal;

                if (orderBy === 'Score') {
                    aVal = parseFloat($(a).data('score')) || 0;
                    bVal = parseFloat($(b).data('score')) || 0;
                    return isDescending ? (bVal - aVal) : (aVal - bVal);
                } else if (orderBy === 'Experience') {
                    var experienceOrder = {'SENIOR': 3, 'MID': 2, 'JUNIOR': 1};
                    aVal = experienceOrder[$(a).data('experience')] || 0;
                    bVal = experienceOrder[$(b).data('experience')] || 0;
                    return isDescending ? (bVal - aVal) : (aVal - bVal);
                } else if (orderBy === 'Recommendation') {
                    var recommendationOrder = {'HIRE': 3, 'CONSIDER': 2, 'REJECT': 1};
                    aVal = recommendationOrder[$(a).data('recommendation')] || 0;
                    bVal = recommendationOrder[$(b).data('recommendation')] || 0;
                    return isDescending ? (bVal - aVal) : (aVal - bVal);
                }
                return 0;
            });

            $.each($rows, function(index, row) {
                $tbody.append(row);
            });

            console.log('Sorted by:', orderBy, '(descending)');
        }

        // Apply all active filters
        function applyFilters() {
            var recommendationValue = $('#recommendationFilter').val();
            var minScoreInput = $('#minScoreFilter');
            var minScoreValue = minScoreInput.length ? (parseFloat(minScoreInput.val()) || 0) : 0;
            var nameValue = $('#nameFilter').val().toLowerCase();
            var experienceValue = $('#experienceFilter').val();
            var recommendationOrderValue = $('#recommendationOrderFilter').val();

            console.log('Debug minScoreFilter element:', minScoreInput.length, 'Value:', minScoreInput.val(), 'Parsed:', minScoreValue);

            console.log('Applying filters:', {
                recommendation: recommendationValue,
                minScore: minScoreValue,
                name: nameValue,
                experience: experienceValue,
                recommendationOrder: recommendationOrderValue
            });

            // Debug: Check first few candidates' data
            $('.candidate-row').slice(0, 3).each(function() {
                var $row = $(this);
                var score = parseFloat($row.attr('data-score')) || 0;
                var name = $row.attr('data-name') || '';
                console.log('Debug candidate:', name, 'Score from data-score:', score, 'Raw data-score:', $row.attr('data-score'));
            });

            var visibleCount = 0;
            var totalCount = 0;

            // Filter each candidate row
            $('.candidate-row').each(function() {
                totalCount++;
                var $row = $(this);
                var recommendation = $row.attr('data-recommendation');
                var experience = $row.attr('data-experience');
                var score = parseFloat($row.attr('data-score')) || 0;
                var name = $row.attr('data-name') || '';

                var show = true;

                // Apply recommendation filter (from main filter)
                if (recommendationValue && recommendation !== recommendationValue) {
                    show = false;
                }

                // Apply recommendation filter (from order by filter)
                if (recommendationOrderValue && recommendation !== recommendationOrderValue) {
                    show = false;
                }

                // Apply experience filter
                if (experienceValue && experience !== experienceValue) {
                    show = false;
                }

                // Apply minimum score filter (only if value is greater than 0)
                if (minScoreValue > 0 && score < minScoreValue) {
                    console.log('Hiding candidate:', name, 'Score:', score, 'Min required:', minScoreValue);
                    show = false;
                }

                // Apply name search filter
                if (nameValue && !name.includes(nameValue)) {
                    show = false;
                }

                // Show or hide the row
                if (show) {
                    $row.show();
                    visibleCount++;
                } else {
                    $row.hide();
                }
            });

            // Update count display
            updateVisibleCount(visibleCount, totalCount);

            // Show/hide clear button
            var orderByValue = $('#orderByFilter').val();
            // Ascending/Descending functionality - Commented for later use
            /*
            var sortOrderValue = $('#sortOrderFilter').val();
            var hasActiveFilters = recommendationValue || minScoreValue > 0 || nameValue ||
                                experienceValue || recommendationOrderValue || orderByValue ||
                                (sortOrderValue && sortOrderValue !== 'desc'); // Show clear if not default desc
            */
            var hasActiveFilters = recommendationValue || minScoreValue > 0 || nameValue ||
                                experienceValue || recommendationOrderValue || orderByValue;
            if (hasActiveFilters) {
                $('#clearFiltersBtn').show();
            } else {
                $('#clearFiltersBtn').hide();
            }
        }

        // Update the visible count display
        function updateVisibleCount(visible, total) {
            if (typeof visible === 'undefined') {
                total = $('.candidate-row').length;
                visible = $('.candidate-row:visible').length;
            }

            var countDisplay = $('#candidateCount');
            if (countDisplay.length === 0) {
                $('#candidatesTable thead tr th:first').append(' <small id="candidateCount" class="text-muted"></small>');
                countDisplay = $('#candidateCount');
            }

            if (visible === total) {
                countDisplay.text('(' + total + ')');
            } else {
                countDisplay.text('(' + visible + ' of ' + total + ')');
            }
        }

        // Clear all filters
        function clearFilters() {
            console.log('Clearing all filters...');
            $('#recommendationFilter').val('');
            $('#orderByFilter').val('');
            // $('#sortOrderFilter').val('desc'); // Reset to default descending - Commented for later use
            $('#minScoreFilter').val('');
            $('#nameFilter').val('');
            $('#experienceFilter').val('');
            $('#recommendationOrderFilter').val('');
            $('#clearFiltersBtn').hide();

            // Reset the order by filter input
            $('#orderByFilterLabel').text('Order By Filter');
            $('#orderByFilterInput').html(`
                <select class="form-select form-select-sm">
                    <option value="">Select a Order By Filter</option>
                </select>
            `);

            // Show all rows
            $('.candidate-row').show();
            updateVisibleCount();
        }

        // Show candidate details in modal
        function showCandidateDetails(candidateEmail) {
            const candidate = candidatesData.find(c => c.email === candidateEmail);
            if (!candidate) return;

            const modalBody = document.getElementById('candidateModalBody');
            const jobAnalysis = candidate.job_analysis || {};
            modalBody.innerHTML = `
                <div class="row">
                    <div class="col-md-6">
                        <div class="card border-0 bg-light mb-3">
                            <div class="card-body">
                                <h6 class="fw-bold mb-3">📊 Scores Breakdown</h6>
                                <div class="row">
                                    <div class="col-6 mb-2">
                                        <small class="text-muted">Final Score</small>
                                        <div class="fw-bold">${candidate.scores.final_score.toFixed(1)}</div>
                                    </div>
                                    <div class="col-6 mb-2">
                                        <small class="text-muted">Skills Match</small>
                                        <div class="fw-bold">${candidate.scores.skills_match.toFixed(1)}</div>
                                    </div>
                                    <div class="col-6 mb-2">
                                        <small class="text-muted">Experience</small>
                                        <div class="fw-bold">${candidate.scores.experience_score.toFixed(1)}</div>
                                    </div>
                                    <div class="col-6 mb-2">
                                        <small class="text-muted">Education</small>
                                        <div class="fw-bold">${candidate.scores.education_score.toFixed(1)}</div>
                                    </div>
                                    <div class="col-6 mb-2">
                                        <small class="text-muted">Keywords</small>
                                        <div class="fw-bold">${candidate.scores.keywords_match.toFixed(1)}</div>
                                    </div>
                                    <div class="col-6 mb-2">
                                        <small class="text-muted">Growth Potential</small>
                                        <div class="fw-bold">${candidate.scores.growth_potential.toFixed(1)}</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-6">
                        <div class="card border-0 bg-light mb-3">
                            <div class="card-body">
                                <h6 class="fw-bold mb-3">💼 Recommendation</h6>
                                <div class="mb-2">
                                    <span class="badge ${candidate.recommendation.decision === 'HIRE' ? 'bg-lime-green' :
                                        candidate.recommendation.decision === 'CONSIDER' ? 'bg-warning' : 'bg-danger'} fs-6 text-white">
                                        ${candidate.recommendation.decision}
                                    </span>
                                    <span class="badge bg-secondary ms-2 text-white">${candidate.recommendation.confidence}</span>
                                </div>
                                <p class="small mb-0">${candidate.recommendation.reason}</p>
                            </div>
                            <div class="card-body">
                                <h6 class="fw-bold mb-3">Contact Details</h6>
                                <div class="mb-2 contact-details">
                                    <div class="contact-item">
                                        <span class="label">Email:</span>
                                        <span class="value">${candidate.email}</span>
                                    </div>
                                    <div class="contact-item">
                                        <span class="label">Contact Number:</span>
                                        <span class="value">${candidate.contact_number}</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-4">
                        <div class="card border-0 bg-light mb-3">
                            <div class="card-body">
                                <h6 class="fw-bold mb-3">🛠️ Skills Analysis</h6>
                                <div class="mb-2">
                                    <small class="text-muted">Match Percentage</small>
                                    <div class="fw-bold text-primary">${candidate.skills_analysis.skill_match_percentage.toFixed(1)}%</div>
                                </div>
                                ${candidate.skills_analysis.matching_skills.length > 0 ? `
                                    <div class="mb-2">
                                        <small class="text-muted">Matching Skills</small>
                                        <div>${candidate.skills_analysis.matching_skills.map(skill =>
                                            `<span class="badge bg-lime-green me-1 mb-1 text-white">${skill}</span>`
                                        ).join('')}</div>
                                    </div>
                                ` : ''}
                                ${candidate.skills_analysis.missing_skills.length > 0 ? `
                                    <div>
                                        <small class="text-muted">Missing Skills</small>
                                        <div>${candidate.skills_analysis.missing_skills.map(skill =>
                                            `<span class="badge bg-danger me-1 mb-1 text-white">${skill}</span>`
                                        ).join('')}</div>
                                    </div>
                                ` : ''}
                            </div>
                        </div>
                    </div>

                    <div class="col-md-4">
                        <div class="card border-0 bg-light mb-3">
                            <div class="card-body">
                                <h6 class="fw-bold mb-3">💼 Experience Analysis</h6>
                                <div class="mb-2">
                                    <small class="text-muted">Level</small>
                                    <div class="fw-bold">${candidate.experience_analysis.experience_level}</div>
                                </div>
                                ${candidate.experience_analysis.matching_experience.length > 0 ? `
                                    <div class="mb-2">
                                        <small class="text-muted">Matching Experience</small>
                                        <ul class="small mb-0">
                                            ${candidate.experience_analysis.matching_experience.map(exp =>
                                                `<li>${exp}</li>`
                                            ).join('')}
                                        </ul>
                                    </div>
                                ` : ''}
                                ${candidate.experience_analysis.experience_gaps.length > 0 ? `
                                    <div>
                                        <small class="text-muted">Experience Gaps</small>
                                        <ul class="small mb-0">
                                            ${candidate.experience_analysis.experience_gaps.map(gap =>
                                                `<li class="text-danger">${gap}</li>`
                                            ).join('')}
                                        </ul>
                                    </div>
                                ` : ''}
                            </div>
                        </div>
                    </div>

                    <div class="col-md-4">
                        <div class="card border-0 bg-light mb-3">
                            <div class="card-body">
                                <h6 class="fw-bold mb-3">🎓 Education Analysis</h6>
                                <div class="mb-2">
                                    <small class="text-muted">Level</small>
                                    <div class="fw-bold">${candidate.education_analysis.education_level}</div>
                                </div>
                                ${candidate.education_analysis.education_highlights.length > 0 ? `
                                    <div>
                                        <small class="text-muted">Highlights</small>
                                        <ul class="small mb-0">
                                            ${candidate.education_analysis.education_highlights.map(highlight =>
                                                `<li>${highlight}</li>`
                                            ).join('')}
                                        </ul>
                                    </div>
                                ` : ''}
                            </div>
                        </div>
                    </div>
                </div>

                ${candidate.assessment.strengths.length > 0 || candidate.assessment.weaknesses.length > 0 ? `
                    <div class="row">
                        <div class="col-12">
                            <div class="card border-0 bg-light mb-3">
                                <div class="card-body">
                                    <h6 class="fw-bold mb-3">📋 Assessment</h6>
                                    <div class="row">
                                        ${candidate.assessment.strengths.length > 0 ? `
                                            <div class="col-md-6">
                                                <small class="text-muted">Strengths</small>
                                                <ul class="small text-success">
                                                    ${candidate.assessment.strengths.map(strength =>
                                                        `<li>${strength}</li>`
                                                    ).join('')}
                                                </ul>
                                            </div>
                                        ` : ''}
                                        ${candidate.assessment.weaknesses.length > 0 ? `
                                            <div class="col-md-6">
                                                <small class="text-muted">Weaknesses</small>
                                                <ul class="small text-danger">
                                                    ${candidate.assessment.weaknesses.map(weakness =>
                                                        `<li>${weakness}</li>`
                                                    ).join('')}
                                                </ul>
                                            </div>
                                        ` : ''}
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                ` : ''}

                <div class="row">
                    <div class="col">
                        <div class="card border-0 bg-light mb-3">
                            <div class="card-body">
                                <h6 class="fw-bold mb-3">🗂️ Job Analysis</h6>
                                <div class="mb-2">
                                    <small class="text-muted">Fresher:</small>
                                    <div class="fw-bold">${jobAnalysis.fresher ? 'Yes' : 'No'}</div>
                                </div>
                                <div class="row">
                                    <div class="mb-2 col">
                                        <small class="text-muted">First Job Start Year:</small>
                                        <div class="fw-bold"> ${jobAnalysis.first_job_start_year != 0 ? jobAnalysis.first_job_start_year : 'N/A' }</div>
                                    </div>
                                    <div class="mb-2 col">
                                        <small class="text-muted">Last Job End Year:</small>
                                        <div class="fw-bold">${jobAnalysis.last_job_end_year != 0 ? jobAnalysis.last_job_end_year :'N/A'}</div>
                                    </div>
                                    <div class="mb-2 col">
                                        <small class="text-muted">Total Jobs Count:</small>
                                        <div class="fw-bold">${jobAnalysis.total_jobs_count != 0 ? jobAnalysis.total_jobs_count :'N/A'}</div>
                                    </div>
                                </div>
                                <div class="mb-2">
                                    <small class="text-muted">Average Job Change:</small>
                                    <div class="fw-bold">${jobAnalysis.average_job_change}</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col">
                        <div class="card border-0 bg-light mb-3">
                        <div class="card-body">
                            <h6 class="fw-bold mb-3">
                            <i class="bi bi-info-circle"></i> Additional Details
                            </h6>
                            <div class="row" style="max-height: 300px; overflow-y: auto;">
                                ${(() => {
                                    const candidateAttributes = attributesByCandidate[candidate.candidate_name] || [];
                                    if (candidateAttributes.length === 0) {
                                        return '<div class="mb-2 col-12"><hr><small class="text-muted"><i class="bi bi-info-circle"></i> No additional details found for this candidate.</small></div>';
                                    }

                                    let attributesHtml = '';
                                    candidateAttributes.forEach(attr => {
                                        attributesHtml += `
                                            <div class="mb-2 col-12 d-flex justify-content-between align-items-start">
                                                <small class="text-muted" style="flex: 0 0 40%;">${attr.attribute_name}:</small>
                                                <div class="fw-bold text-end" style="flex: 1; word-break: break-word;">${attr.attribute_value}</div>
                                            </div>
                                        `;
                                    });
                                    return attributesHtml;
                                })()}

                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="row">
                    <div class="col-md-6">
                        <div class="card border-0 bg-light mb-3">
                            <div class="card-body">
                                <h6 class="fw-bold mb-3">🎯 Hiring Insights</h6>
                                <div class="mb-2">
                                    <small class="text-muted">Salary Alignment</small>
                                    <div class="fw-bold">${candidate.hiring_insights.salary_expectation_alignment}</div>
                                </div>
                                <div class="mb-2">
                                    <small class="text-muted">Onboarding Priority</small>
                                    <div class="fw-bold">${candidate.hiring_insights.onboarding_priority}</div>
                                </div>
                                ${candidate.hiring_insights.interview_focus_areas.length > 0 ? `
                                    <div>
                                        <small class="text-muted">Interview Focus Areas</small>
                                        <div>${candidate.hiring_insights.interview_focus_areas.map(area =>
                                            `<span class="badge bg-info me-1 mb-1 text-white">${area}</span>`
                                        ).join('')}</div>
                                    </div>
                                ` : ''}
                            </div>
                        </div>
                    </div>

                    <div class="col-md-6">
                        <div class="card border-0 bg-light mb-3">
                            <div class="card-body">
                                <h6 class="fw-bold mb-3">📄 File Information</h6>
                                <div class="small">
                                    <div class="mb-1"><strong>Filename:</strong> ${candidate.filename}</div>
                                    <div class="mb-1"><strong>Word Count:</strong> ${candidate.metadata.word_count}</div>
                                    <div class="mb-1"><strong>Processing Time:</strong> ${candidate.metadata.processing_time.toFixed(2)}s</div>
                                    <div class="mb-1"><strong>Processed At:</strong> ${new Date(candidate.metadata.processed_at).toLocaleString()}</div>
                                </div>
                                ${candidate.summary ? `
                                    <div class="mt-2">
                                        <small class="text-muted">Summary</small>
                                        <p class="small mb-0">${candidate.summary}</p>
                                    </div>
                                ` : ''}
                            </div>
                        </div>
                    </div>
                </div>
            `;

            document.getElementById('candidateModalLabel').textContent = `${candidate.candidate_name} - Detailed Analysis`;
            $('#candidateModal').modal('show');
        }

        function showCustomAttributesForm(candidate_name , candidate_filename) {
            $('#customAttributesModalLabel').text('Add Details for ' + candidate_name);

            // 2. Set the value of the hidden input field.
            // This is useful for form submission.
            $('#candidateIdentifierByName').val(candidate_name);
            $('#candidateIdentifierByFileName').val(candidate_filename);
            
            $('#dynamic-fields-container').empty();

            addNewDetailRow();

            $('#customAttributesModal').modal('show');
        }

        // Function to add a new key-value pair row
            const addNewDetailRow = () => {
                // Create the HTML for the new row. No need for unique IDs for removal with jQuery.
                const container = $('#dynamic-fields-container');
                
                const newRowHTML = `
                    <div class="input-group mb-3">
                        <input type="text" class="form-control" name="custom_key" placeholder="Field Name" required>
                        <input type="text" class="form-control" name="custom_value" placeholder="Value" required>
                        <button class="btn btn-outline-danger remove-row-btn" type="button">Remove</button>
                    </div>
                `;
                // Append the new row to the container
                container.append(newRowHTML);
            };

        {% comment %} // Export to CSV functionality
        function exportToCSV() {
            const headers = ['Name', 'Filename', 'Final Score', 'Skills Match %', 'Experience Level', 'Recommendation', 'Reason'];
            const csvContent = [
                headers.join(','),
                ...candidatesData.map(candidate => [
                    `"${candidate.candidate_name}"`,
                    `"${candidate.filename}"`,
                    candidate.scores.final_score.toFixed(1),
                    candidate.skills_analysis.skill_match_percentage.toFixed(1),
                    candidate.experience_analysis.experience_level,
                    candidate.recommendation.decision,
                    `"${candidate.recommendation.reason}"`
                ].join(','))
            ].join('\n');

            const blob = new Blob([csvContent], { type: 'text/csv' });
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = 'resume_analysis_report.csv';
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            window.URL.revokeObjectURL(url);
        } {% endcomment %}
    </script>
{% endblock scripts %}

{% block content %}
<br><br>
<div class="container-fluid mt-4">
    <!-- Header Section -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card border-0 shadow-sm bg-primary text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h2 class="mb-1 fw-bold">📊 Resume Analysis Report</h2>
                            <p class="mb-0 opacity-75">
                                {% comment %} {% if job_code == "PY" %}
                                    Python Developer
                                {% elif job_code == "JV" %}
                                    Java Developer
                                {% endif %} {% endcomment %}
                                {{job_title}} - {{job_code}}
                            </p>
                        </div>
                        <div>
                            <button class="btn btn-light btn-sm mt-auto" id="openModalBtn">New Analysis</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    {% comment %} {% if file_content.boiler_plate == 0 %} {% endcomment %}
    {% comment %} {% if file_content.candidates %} {% endcomment %}
        
       <div id="candidatesContainer" style="{% if not file_content.candidates %}display: none;{% endif %}">
             {% comment %} <!-- Summary Statistics -->
            <div class="row mb-4">
                <div class="col-md-4">
                    <div class="card border-0 shadow-sm h-100">
                        <div class="card-body text-center">
                            <div class="display-4 text-primary mb-2">{{ file_content.summary_statistics.total_candidates }}</div>
                            <h6 class="text-muted mb-0">Total Candidates</h6>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="card border-0 shadow-sm h-100">
                        <div class="card-body text-center">
                            <div class="display-4 text-success mb-2">{{ file_content.summary_statistics.successful_analyses }}</div>
                            <h6 class="text-muted mb-0">Successful Analyses</h6>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="card border-0 shadow-sm h-100">
                        <div class="card-body text-center">
                            <div class="display-4 text-warning mb-2">{{ file_content.summary_statistics.average_score|floatformat:1 }}</div>
                            <h6 class="text-muted mb-0">Average Score</h6>
                        </div>
                    </div>
                </div>
                
            </div>

            <!-- Score Distribution and Recommendations -->
            <div class="row mb-4">
                <div class="col-md-6">
                    <div class="card border-0 shadow-sm h-100">
                        <div class="card-header bg-light border-0">
                            <h5 class="mb-0 fw-bold">📈 Score Distribution</h5>
                        </div>
                        <div class="card-body">
                            <div class="row text-center">
                                <div class="col-6 col-md-3 mb-3">
                                    <div class="badge bg-lime-green fs-6 mb-2 text-white">{{ file_content.summary_statistics.score_distribution.excellent }}</div>
                                    <div class="small text-muted">Excellent</div>
                                </div>
                                <div class="col-6 col-md-3 mb-3">
                                    <div class="badge bg-info fs-6 mb-2 text-white">{{ file_content.summary_statistics.score_distribution.good }}</div>
                                    <div class="small text-muted">Good</div>
                                </div>
                                <div class="col-6 col-md-3 mb-3">
                                    <div class="badge bg-warning fs-6 mb-2 text-white">{{ file_content.summary_statistics.score_distribution.average }}</div>
                                    <div class="small text-muted">Average</div>
                                </div>
                                <div class="col-6 col-md-3 mb-3">
                                    <div class="badge bg-danger fs-6 mb-2 text-white">{{ file_content.summary_statistics.score_distribution.below_average }}</div>
                                    <div class="small text-muted">Below Avg</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="card border-0 shadow-sm h-100">
                        <div class="card-header bg-light border-0">
                            <h5 class="mb-0 fw-bold">💼 Hiring Recommendations</h5>
                        </div>
                        <div class="card-body">
                            <div class="row text-center">
                                <div class="col-4 mb-3">
                                    <div class="badge bg-lime-green fs-6 mb-2 text-white">{{ file_content.summary_statistics.recommendations.HIRE }}</div>
                                    <div class="small text-muted">Hire</div>
                                </div>
                                <div class="col-4 mb-3">
                                    <div class="badge bg-warning fs-6 mb-2 text-white">{{ file_content.summary_statistics.recommendations.CONSIDER }}</div>
                                    <div class="small text-muted">Consider</div>
                                </div>
                                <div class="col-4 mb-3">
                                    <div class="badge bg-danger fs-6 mb-2 text-white">{{ file_content.summary_statistics.recommendations.REJECT }}</div>
                                    <div class="small text-muted">Reject</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div> {% endcomment %}

            <div id="summaryContainer" >

            </div>

            <!-- Job Description -->
            <div class="row mb-4">
                <div class="col-12">
                    <div class="card border-0 shadow-sm">
                        <div class="card-header bg-light border-0">
                            <h5 class="mb-0 fw-bold">📋 Job Description</h5>
                        </div>
                        <div class="card-body">
                            <div class="bg-light p-3 rounded job-description-content">
                                {% comment %} <pre class="mb-0 text-wrap" style="white-space: pre-wrap;">{{ file_content.meta.job_description }}</pre> {% endcomment %}
                                {{ file_content.job_description_html |safe }}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            

            <!-- Top Candidates Section -->
            {% comment %} {% if file_content.top_candidates %}
            <div class="row mb-4">
                <div class="col-12">
                    <div class="card border-0 shadow-sm">
                        <div class="card-header bg-light border-0">
                            <h5 class="mb-0 fw-bold">🏆 Top Candidates</h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                {% for candidate in file_content.top_candidates %}
                                    {% if candidate.recommendation.decision == "HIRE" %}
                                        <div class="col-md-6 col-lg-4 mb-3">
                                            <div class="card border-start border-primary border-3 h-100">
                                                <div class="card-body">
                                                    <h6 class="fw-bold text-primary mb-2">{{ candidate.candidate_name }}</h6>
                                                    <p class="small text-muted mb-2">{{ candidate.filename }}</p>

                                                    <div class="mb-2">
                                                        <span class="badge bg-primary text-white">Score: {{ candidate.scores.final_score|floatformat:1 }}</span>
                                                        <span class="badge bg-lime-green text-white">{{ candidate.recommendation.decision }}</span>
                                                    </div>

                                                    <div class="small">
                                                        <div class="mb-1">
                                                            <strong>Skills Match:</strong> {{ candidate.skills_analysis.skill_match_percentage|floatformat:1 }}%
                                                        </div>
                                                        <div class="mb-1">
                                                            <strong>Experience:</strong> {{ candidate.experience_analysis.experience_level }}
                                                        </div>
                                                        {% if candidate.summary %}
                                                        <div class="text-muted">
                                                            <em>{{ candidate.summary|truncatechars:100 }}</em>
                                                        </div>
                                                        {% endif %}
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    {% endif %}
                                {% endfor %}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            {% endif %} {% endcomment %}

            <!-- Candidates Table -->
            <div class="row mb-4" id="candidates-table" style="min-height: 80vh;">
                <div class="col-12">
                    <div class="card border-0 shadow-sm">
                        <div class="card-header bg-light border-0 d-flex justify-content-between align-items-center">
                            <h5 class="mb-0 fw-bold">👥 All Candidates</h5>
                            <div>
                                {% comment %} <button class="btn btn-sm btn-outline-primary" onclick="exportToCSV()">
                                    <i class="bi bi-download"></i> Export CSV
                                </button> {% endcomment %}
                                <button class="btn btn-sm btn-outline-secondary" onclick="toggleFilters()">
                                    <i class="bi bi-funnel"></i> Filters
                                </button>
                                <button class="btn btn-sm btn-outline-warning" onclick="clearFilters()" style="display: none;" id="clearFiltersBtn">
                                    <i class="bi bi-x-circle"></i> Clear
                                </button>
                            </div>
                        </div>

                        <!-- Filters Panel -->
                        <div id="filtersPanel" class="card-body border-bottom bg-light" style="display: none;">
                            <div class="row">
                                
                                <div class="col-md-2">
                                    <label class="form-label small">Order By</label>
                                    <select class="form-select form-select-sm" id="orderByFilter">
                                        <option value="">None</option>
                                        <option value="Score">Score</option>
                                        <option value="Experience">Experience</option>
                                        <option value="Recommendation">Recommendation</option>
                                    </select>
                                </div>
                                <!-- Ascending/Descending Sort Order - Commented for later use
                                <div class="col-md-2">
                                    <label class="form-label small">Sort Order</label>
                                    <select class="form-select form-select-sm" id="sortOrderFilter">
                                        <option value="desc">Descending</option>
                                        <option value="asc">Ascending</option>
                                    </select>
                                </div>
                                -->
                                <div class="col-md-3" id="orderByFilterOptionsDiv">
                                    <label class="form-label small" id="orderByFilterLabel">Order By Filter</label>
                                    {% comment %} <input type="number" class="form-control form-control-sm" id="minScoreFilter" placeholder="0" min="0" max="100"> {% endcomment %}
                                    <div id="orderByFilterInput">
                                        <select class="form-select form-select-sm">
                                            <option value="">Select a Order By Filter</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <label class="form-label small">Search Name</label>
                                    <input type="text" class="form-control form-control-sm" id="nameFilter" placeholder="Search candidate...">
                                </div>
                                {% comment %} <div class="col-md-12 mt-2">
                                    <button type="button" class="btn btn-sm btn-info" onclick="testFilters()">Test Filters</button>
                                </div> {% endcomment %}
                            </div>
                        </div>

                        <div class="card-body p-0">
                            <div class="table-responsive">
                                <table class="table table-hover mb-0" id="candidatesTable">
                                    <thead class="table-light">
                                        <tr>
                                            <th class="border-0">Candidate</th>
                                            <th class="border-0">Score</th>
                                            <th class="border-0">Skills Match</th>
                                            <th class="border-0">Experience</th>
                                            <th class="border-0">Recommendation</th>
                                            <th class="border-0">Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {% for candidate in file_content.candidates %}
                                        <tr class="candidate-row"
                                            data-recommendation="{{ candidate.recommendation.decision }}"
                                            data-experience="{{ candidate.experience_analysis.experience_level }}"
                                            data-score="{{ candidate.scores.final_score }}"
                                            data-name="{{ candidate.candidate_name|lower }}">
                                            <td>
                                                <div>
                                                    <strong>{{ candidate.candidate_name }}</strong>
                                                    <br>
                                                    <small class="text-muted">{{ candidate.filename }}</small>
                                                </div>
                                            </td>
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    <div class="progress me-2" style="width: 60px; height: 8px;">
                                                        <div class="progress-bar
                                                            {% if candidate.scores.final_score >= 80 %}bg-lime-green
                                                            {% elif candidate.scores.final_score >= 60 %}bg-info
                                                            {% elif candidate.scores.final_score >= 40 %}bg-warning
                                                            {% else %}bg-danger{% endif %} text-white"
                                                            style="width: {{ candidate.scores.final_score }}%"></div>
                                                    </div>
                                                    <span class="fw-bold">{{ candidate.scores.final_score|floatformat:1 }}</span>
                                                </div>
                                            </td>
                                            <td>
                                                <span class="badge
                                                    {% if candidate.skills_analysis.skill_match_percentage >= 70 %}bg-lime-green
                                                    {% elif candidate.skills_analysis.skill_match_percentage >= 50 %}bg-info
                                                    {% elif candidate.skills_analysis.skill_match_percentage >= 30 %}bg-warning
                                                    {% else %}bg-danger{% endif %} text-white">
                                                    {{ candidate.skills_analysis.skill_match_percentage|floatformat:1 }}%
                                                </span>
                                            </td>
                                            <td>
                                                <span class="badge
                                                    {% if candidate.experience_analysis.experience_level == "SENIOR" %}bg-lime-green
                                                    {% elif candidate.experience_analysis.experience_level == "MID" %}bg-info
                                                    {% else %}bg-warning{% endif %} text-white">
                                                    {{ candidate.experience_analysis.experience_level }}
                                                </span>
                                            </td>
                                            <td>
                                                {% if candidate.recommendation.decision == "HIRE" %}
                                                    <span class="badge bg-lime-green text-white">{{ candidate.recommendation.decision }}</span>
                                                {% elif candidate.recommendation.decision == "CONSIDER" %}
                                                    <span class="badge bg-warning text-white">{{ candidate.recommendation.decision }}</span>
                                                {% else %}
                                                    <span class="badge bg-danger text-white">{{ candidate.recommendation.decision }}</span>
                                                {% endif %}
                                            </td>
                                            <td>
                                                <button class="btn btn-sm btn-outline-primary me-1" onclick="showCandidateDetails('{{ candidate.email }}')">
                                                    <i class="bi bi-eye"></i> Details
                                                </button>
                                                <button class="btn btn-sm btn-outline-primary" onclick="showCustomAttributesForm('{{ candidate.candidate_name }}' , '{{ candidate.filename }}')" title="Add">
                                                    <i class="bi bi-plus"></i>
                                                </button>
                                            </td>
                                        </tr>
                                        {% endfor %}
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
       </div>
    {% comment %} {% else %} {% endcomment %}
        <h1 class="text-center text-danger p-3" id="no-analysis-found" style="{% if file_content.candidates %}display: none;{% endif %}">No Analysis Found.</h1>
    {% comment %} {% endif %} {% endcomment %}
</div>

<!-- Custom Attributes Modal -->
<div class="modal fade" id="customAttributesModal" tabindex="-1" aria-labelledby="customAttributesModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="customAttributesModalLabel">Add Details</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body" id="customAttributesModalBody">
                    
                    <!-- Form to hold dynamic fields -->
                    <form id="dynamic-form">
                        <input type="hidden" id="candidateIdentifierByName" name="candidate_name" value="">
                        <input type="hidden" id="candidateIdentifierByFileName" name="candidate_name" value="">
                        <!-- Container where new rows will be injected -->
                        <div id="dynamic-fields-container">
                            <!-- Initial row can be added here if needed -->
                        </div>

                        <!-- Button to add a new row of fields -->
                        <div class="d-flex justify-content-end border-top pt-3 mt-3">
                             <button type="button" class="btn btn-success btn-sm" id="addFieldBtn">
                                + Add Field
                            </button>
                        </div>
                        
                        <!-- Static submit button at the end of the form -->
                        <div class="modal-footer mt-4">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                            <button type="submit" class="btn btn-primary">Add Details</button>
                        </div>
                    </form>
                </div>
        </div>
    </div>
</div>
<!-- Candidate Details Modal -->
<div class="modal fade" id="candidateModal" tabindex="-1" aria-labelledby="candidateModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="candidateModalLabel">Candidate Details</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body" id="candidateModalBody">
                <!-- Content will be populated by JavaScript -->
            </div>
        </div>
    </div>
</div>

<!-- Modal -->
<div class="modal fade" id="myModal" tabindex="-1" aria-labelledby="myModalLabel" aria-hidden="true">
  <div class="modal-dialog modal-md">
    <div class="modal-content border-0 shadow">
      <div class="modal-header bg-primary text-white">
        <h5 class="modal-title mb-0" id="myModalLabel">
          Resume Analysis in Progress
        </h5>
        <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
      </div>
      <div class="modal-body d-flex flex-column align-items-center text-center py-4">
        <div class="spinner-border text-primary mb-3" role="status">
          <span class="visually-hidden"></span>
        </div>
        <p class="fw-semibold mb-0">
          Started resume analysis for the job description.<br>
          Waiting for it to complete...
        </p>
      </div>
    </div>
  </div>
</div>

<!-- Bottom-right progress popup -->
<div id="progressPopup" class="alert alert-info position-fixed" style="
    bottom: 20px;
    right: 20px;
    z-index: 9999;
    min-width: 300px;
    max-width: 400px;
    display: none;
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
    border-radius: 8px;
">
    <i class="bi bi-activity"></i> Analysis in progress...
</div>

<style>

    #progressPopup {
        animation: slideInRight 0.5s ease-out;
    }

    @keyframes slideInRight {
        from {
            transform: translateX(100%);
            opacity: 0;
        }
        to {
            transform: translateX(0);
            opacity: 1;
        }
    }

    #customAttributesModalBody {
        height: 80vh;
    }

    .bg-lime-green {
    background-color: #32CD32 !important; /* This is the hex code for LimeGreen */
    color: white !important; /* White text provides good contrast */
    }

    .contact-details {
        margin-bottom: 1rem;
    }

    .contact-item {
        margin-bottom: 0.5rem;
    }

    .contact-item .label {
        font-weight: bold;
        margin-right: 0.5rem;
    }

    .contact-item .value {
        font-weight: normal;
    }

    #no-analysis-found {
        height: 60vh;
    }

    /* Override global p tag centering for job description */
    .job-description-content p {
        text-align: left !important;
    }

</style>

{% endblock content %}