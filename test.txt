
-----------------RAW LLM RESPONSE------------------
{
    "candidate_name": "<PERSON><PERSON><PERSON><PERSON>",
    "overall_score": 85,
    "skills_match": 80,
    "experience_relevance": 90,
    "education": 75,
    "keywords_match": 70,
    "overall_fit": 82,
    "matching_skills": ["Python", "MySQL", "Git"],
    "missing_skills": ["Problem-solving and algorithmic thinking"],
    "matching_experience": ["Building backend logic using Python", "Managing MySQL databases for CRUD operations"],
    "experience_gaps": [],
    "education_highlights": ["Bachelor of Technology - BTech, Computer and communication (2018-2022)"],
    "strengths": ["Strong problem-solving skills", "Experience with version control systems like Git"],
    "weaknesses": ["Limited experience in data-driven applications"],
    "growth_potential": 80,
    "cultural_fit_indicators": [],
    "salary_expectation_alignment": "MEDIUM",
    "recommendation": "CONSIDER",
    "recommendation_reason": "Strong technical skills, but limited experience in required areas",
    "summary": "<PERSON><PERSON><PERSON><PERSON> is a software engineer with strong technical skills and experience working with Python, MySQL, and Git. She has a solid educational background in computer science and communication.",
    "interview_focus_areas": ["Problem-solving and algorithmic thinking", "Data-driven applications"],
    "matching_skills_count": 3,
    "missing_skills_count": 1,
    "relevant_experience_years": 2,
    "education_level_code": 2
}
---------------------------------------------------

-----------------Resume Text------------------
VENKATA JAYA SIVARAM GAJIVELLI Vijayawada, Andhra Pradesh, India   +************   <EMAIL> PROFILES: GitHub Profile: Sivaram-Gajivelli LinkedIn Profile: Sivaram G V J  LinkedIn OBJECTIVE Enthusiastic and detail-oriented Computer Science Engineering student with a strong foundation in cybersecurity, web development, and embedded systems. Passionate about designing secure and efficient solutions, leveraging cutting-edge technologies, and contributing to innovative projects that solve real-world challenges. EDUCATION TECHNICAL SKILLS Programming Languages: Python, JavaScript, C, C++ Web Development: HTML, CSS, JavaScript, AngularJS, Node.js, Express.js, MongoDB Database Management: SQLite, MySQL, Firebase Tools  Technologies: Git, Tkinter PROJECTS Security Policy Management System Developed a Tkinter-based UI application for creating and managing organizational security policies. Implemented SHA-256 password encryption and two-step email verification. Integrated SQLite for secure data storage. INTERNSHIPS  EXPERIENCE Cybersecurity Intern Supraja Technologies Pvt. Ltd, 3 Months Developed and implemented security policies for organizations. Conducted penetration testing and vulnerability assessments. Strengthened authentication protocols using SHA-256 encryption. Web Development Intern Swecha Organization, 2 Months Developed and implemented a web application using HTML, CSS and JavaScript. Rowboatics Competition Bot Designed a manually controlled, battery-powered boat for obstacle navigation for Rowboatic competitions in IIT Bombay. Ensured compliance with dimension and power constraints while maintaining high maneuverability. CodeDecode Competition A coding competition conducted in IIT Bombay TechFest, where a few coding question were asked in the coding competition. e-Yantra Robotic Competitions (2024) Qualified to 2nd stage in E-Yantra Competition conducted by IIT Bombay 2024 for the theme EcoMendor Bot. CERTIFICATIONS  ACHIVEMENTS Python Basics Hacker Rank Dashboard  HackerRank Certified in Ethical Hacking and Cybersecurity (Supraja Technologies) Certified in Web Development (Swecha Organization) LANGUAGES English Telugu Potti Sriramulu Chalavadi Mallikarjuna Rao College of Engineering  Technology Vijayawada CGPA: 8.11 Sept 2022 - Present Bachelor Of Technology Sri Bhavishya Junior College Vijayawada Sep 2020 - May 2022 INTERMEDIATE St. Anns EM High School Vijayawada Jun 2019 - Apr 2020 ISCE
---------------------------------------------------

-----------------RAW LLM RESPONSE------------------
No professional experience found
---------------------------------------------------

-----------------Cleaned Experience------------------
No professional experience found
---------------------------------------------------

-----------------RAW LLM RESPONSE------------------
{
    "candidate_name": "Venkata Jaya Sivaram Gajivelli",
    "overall_score": 85,
    "skills_match": 80,
    "experience_relevance": 70,
    "education": 90,
    "keywords_match": 75,
    "overall_fit": 82,
    "matching_skills": ["Python", "JavaScript", "C", "C++", "HTML", "CSS", "AngularJS", "Node.js", "Express.js", "MongoDB", "SQLite", "MySQL", "Firebase", "Git"],
    "missing_skills": ["Django/Flask (implied by Python)", "Spring (not mentioned, but implied by Java skills)"],
    "matching_experience": ["Cybersecurity Intern Supraja Technologies Pvt. Ltd", "Web Development Intern Swecha Organization"],
    "experience_gaps": ["No experience in backend development with a focus on clean data structures and MySQL queries"],
    "education_highlights": ["Bachelor Of Technology, Potti Sriramulu Chalavadi Mallikarjuna Rao College of Engineering  Technology Vijayawada CGPA: 8.11 Sept 2022 - Present"],
    "strengths": ["Strong foundation in cybersecurity, web development, and embedded systems", "Passionate about designing secure and efficient solutions"],
    "weaknesses": ["Limited experience in backend development with a focus on clean data structures and MySQL queries"],
    "growth_potential": 90,
    "cultural_fit_indicators": ["Mentorship from senior developers", "Real-world projects to build portfolio", "Friendly, collaborative work culture"],
    "salary_expectation_alignment": "LOW",
    "recommendation": "HIRE",
    "recommendation_reason": "Strong foundation in relevant skills and technologies, with potential for growth into full-stack development roles.",
    "summary": "Enthusiastic and detail-oriented Computer Science Engineering student with a strong foundation in cybersecurity, web development, and embedded systems. Passionate about designing secure and efficient solutions, leveraging cutting-edge technologies, and contributing to innovative projects that solve real-world challenges.",
    "interview_focus_areas": ["Backend development experience", "MySQL query optimization", "Clean data structures"],
    "matching_skills_count": 13,
    "missing_skills_count": 2,
    "relevant_experience_years": 1.5,
    "education_level_code": 3
}
---------------------------------------------------

-----------------Resume Text------------------
MAMADI JOVEL CAREER OBJECTIVE: To seek an intensing, innovative and creative career in reputed companies or institutions where I can exercise my skills and contribute effectively for the progress of the organisation. ACADEMICS: B. Tech (CSE)  65 PSCMRCET  JNTUK  September 2022  Present Diploma (CIVIL)  77 Dasari Jhansi Rani College Of Engineering And technology July 2020  March 2022 SSC  85 GNRMC High School  SSC Board  July 2018  April 2019 WORK EXPERIENCE (INTERHSIPS): SwechaAP, Vijayawada [June 2024  July 2024] I have an experience of 2months as an intern in the field of WEB TECHNOLOGY. Based on my Experience I developed a web application on WEATHER PATTERNS. Supraja Technologies, Vijayawada [May 2024  July 2024] I have an experience of 2months as an intern in the field of CYBERSECURITY. Based on my Experience I developed a project on SECURITY POLICY MANAGEMENT. SKILLS: PYTHON C(Basics) JAVA HTML CSS JAVASCRIPT MIT APP INVENTOR ARDUINO UNO NODE MCU CERTIFICATIONS: Introduction to IoT by Simplilearn  March 2024 Python Intermediate by Solo learn  May 2023 C by Solo learn  June 2023 JAVA Intermediate by Solo learn  December 2023 ACHIVEMENTS: Participated in IoT Inventors Hackathon by Eduino Robots in July  2024. Participated in CyberAce Hackathon by Supraja Technologies in March  2024. Represented at the District Level Tennikoit player. PROJECTS: IoT-Based Projects: CONTROLLING APPLIANCES Team Members: M. Jovel G. Sri Supriya N. Jyothi Rajyalakshmi T. Satish Babu Role in the Project: Manager Description: Controlling Appliances is the automatic lights and fans and controlling it with the sensors. Cybersecurity-Based Projects: SECURITY POLICY MANAGEMENT Team Members: M. Jovel, G. Sri Supriya T. Satish Babu, A. Sasidhar, G. Sivaram. Description: Security Policy Management it creates a policies to the users for the security of the systems and it provides update policies, new policies . WORKSHOPS ATTENDED: Participated in IoT workshop by Eduino Robots in July  2024. Participated in Cyber Security Workshop by Supraja Technologies in March  2024. Participated in App Development in June- 2024. HOBBIESINTERSETS: I have Team working capability. Exploring mobile and Web Technologies. I have few problem solving skills. Playing Tennikoit. PERSONAL DETAILS: Phone No.: 9701179320 Email id: <EMAIL> Linked in: https://www.linkedin.com/in/jovel-mamidi-244a42316?utm_sourceshareutm_campaignshare_viautm_contentprofileutm_mediumandroid_app DECLARATION: I hereby declare that the above given information is true to my knowledge and belief. Date: Signature: Place: Vijayawada M.Jovel Programming Frontend Tools
---------------------------------------------------

-----------------RAW LLM RESPONSE------------------
No professional experience found
---------------------------------------------------

-----------------Cleaned Experience------------------
No professional experience found
---------------------------------------------------

-----------------RAW LLM RESPONSE------------------
{
    "candidate_name": "MAMADI JOVEL",
    "overall_score": 82,
    "skills_match": 80,
    "experience_relevance": 70,
    "education": 65,
    "keywords_match": 85,
    "overall_fit": 78,
    "matching_skills": ["Python", "C(Basics)", "JAVA", "HTML", "CSS", "JAVASCRIPT"],
    "missing_skills": ["MySQL queries", "Git", "Problem-solving and algorithmic thinking"],
    "matching_experience": ["Web application on WEATHER PATTERNS", "Project on SECURITY POLICY MANAGEMENT"],
    "experience_gaps": [],
    "education_highlights": ["B. Tech (CSE)", "Diploma (CIVIL)"],
    "strengths": ["Team working capability", "Exploring mobile and Web Technologies", "Problem solving skills"],
    "weaknesses": ["Limited experience in required skills", "No direct mention of OOP concepts"],
    "growth_potential": 80,
    "cultural_fit_indicators": [],
    "salary_expectation_alignment": "MEDIUM",
    "recommendation": "CONSIDER",
    "recommendation_reason": "Strong foundation in programming, but lacks experience in required skills and direct mention of OOP concepts.",
    "summary": "A fresher with a strong foundation in programming, looking to start their journey in backend development. Has some relevant experience in web technology and cybersecurity, but needs improvement in required skills.",
    "interview_focus_areas": ["Problem-solving and algorithmic thinking", "OOP concepts", "MySQL queries"],
    "matching_skills_count": 6,
    "missing_skills_count": 3,
    "relevant_experience_years": 0.5,
    "education_level_code": 2
}
---------------------------------------------------

-----------------Resume Text------------------
THOTASATISHBABU Vijayawada,AndhraPradesh,<EMAIL>+919492287814 Profiles GitHubLinkedin CareerObjective Iamafastlearnerwiththeabilitytoadapttoanyenvironment.Ihaveastronginterestinlearningnew technologiesandameagertoworkwithateam.Ialsopossessleadershipqualitiesandcantakeon responsibilitieswhenneeded.MygoalistocontributeeffectivelytothecompanyIjoin.Iamcommittedto continuouslygrowingandimprovinginmyrole. Education BachelorofTechnologyinComputerScienceEngineering(CSE) PottiSriramuluChaluvadiMallikarjunaRaoCollegeofEngineeringandTechnologyAffiliatedby JNTUKSeptember2022Present Intermediate(10+2) SyedAppalaSwamyCollegeSeptember2020May2022 SSC(10thGrade) S.D.M.Y.R.RHighSchool,SSCBoardJuly2019April2020 Skills Programming:C,Python,Java WebDevelopment:HTML,CSS,JavaScript,PHP EmbeddedSystems:Arduino,NodeMCU,RaspBerryPi Cybersecurity:EthicalHacking,Cryptography Databases:MySQL,SQLite,Firebase OtherTools:Git,XAMPP,QuartusPrime,TinkerCad,Fritzing SoftSkills:Problem-Solving,Teamwork,Leadership Projects 1.SecurityPolicyManagementSystem(Github) DevelopedaPythonTkinterUIapplicationtomanageorganizationalpolicies. ImplementedSHA-256encryption,2-stepemailverification,andSQLitedatabase. 2.AIVOICEAssistantrobot Designedarobottoworkwithvoicecommandsusingpythoncode ControlledtherobotusingArduinonanoviaserialcommunication 3.ESP32-CAMTelegramBot DesignedanESP32-CAMsystemthattorecognizethefaceforsecurityalsocaptureandsend imagesviaaTelegrambot. UsedFirebaseTelegramAPIforseamlessinteraction. 4.ControllingAppliances DesignedaSystemtocontroltheappliancesusingtheMobilePhone AutomaticturnoffandturnonSystembasedontheconditions Internships CybersecurityInternSuprajaTechnologies WorkedonaProjectnamedasSecurityPolicyManagement. SecurityPolicyManagementisthetooldevelopedbypythonforwindowsfororganizations. InternetofthingsInternEduinoRobots WorkedondifferentkindsofprojectsbasedontheInternetofthings. FewoftheprojectsthatIworkedareHomeAutomation,Facerecognition,ChildSecurityDevice. CertificationsAchievements Qualifiedto2ndstageinEyantraCompetitionconductedbyIITBombay2024 PythonBasicCertificatefromHackerRank ExcellenceCertificateinCyberacehackathonconductedbySuprajaTechnologies PythonProgrammingcoursecompletioncertificateincodetantra. ExtracurricularActivities ParticipatedincodeDecode,RowBoaticscompetitionTechfest2024ofIITBombay ParticipatedintheIoTInventorsWorkshopconductedbyEduinoRobots ParticipatedintheAppDevelopmentworkshopinTechfest2024 ParticipatedintheCyberSecurityWorkshopconductedbySuprajaTechnologies Languages 1.Telugu2.English3.Hindi Declaration Iherebydeclarethattheabovegiveninformationistruetomyknowledgeandbelief. Date: Signature: Place:Vijayawada ThotaSatishBabu
---------------------------------------------------

-----------------RAW LLM RESPONSE------------------
Software Engineer - Supraja Technologies (Not specified)
Worked on a Project named as Security Policy Management.

Cybersecurity Intern - Supraja Technologies (Not specified)
Developed the tool Security Policy Management using Python for Windows for organizations.

No professional experience found.
---------------------------------------------------

-----------------Cleaned Experience------------------
Software Engineer - Supraja Technologies (Not specified)
Worked on a Project named as Security Policy Management.
Cybersecurity Intern - Supraja Technologies (Not specified)
Developed the tool Security Policy Management using Python for Windows for organizations.
---------------------------------------------------

-----------------RAW LLM RESPONSE------------------
{
    "first_job_start_year": 0,
    "last_job_end_year": 0,
    "total_jobs_count": 0
}
---------------------------------------------------

-----------------RAW LLM RESPONSE------------------
{
    "candidate_name": "Thotasatishbabu",
    "overall_score": 80,
    "skills_match": 85,
    "experience_relevance": 70,
    "education": 75,
    "keywords_match": 90,
    "overall_fit": 82,
    "matching_skills": ["Python", "MySQL", "Git", "Problem-Solving", "Teamwork", "Leadership"],
    "missing_skills": ["OOP concepts", "Algorithmic thinking"],
    "matching_experience": ["SecurityPolicyManagementSystem (Github)", "AIVOICEAssistantrobot", "ESP32-CAMTelegramBot", "ControllingAppliances"],
    "experience_gaps": [],
    "education_highlights": ["Bachelor of Technology in Computer Science Engineering (CSE)"],
    "strengths": ["Fast learner, adaptability, leadership qualities, problem-solving skills"],
    "weaknesses": ["Limited experience with OOP concepts and algorithmic thinking"],
    "growth_potential": 85,
    "cultural_fit_indicators": ["Friendly, collaborative work culture", "Mentorship from senior developers"],
    "salary_expectation_alignment": "LOW",
    "recommendation": "HIRE",
    "recommendation_reason": "Strong foundation in Python and MySQL, good problem-solving skills, and leadership qualities.",
    "summary": "Thotasatishbabu is a fresher with a strong interest in learning new technologies. He has a solid understanding of Python fundamentals and experience working with MySQL databases. His projects showcase his ability to adapt to any environment and take on responsibilities when needed.",
    "interview_focus_areas": ["OOP concepts", "Algorithmic thinking"],
    "matching_skills_count": 5,
    "missing_skills_count": 2,
    "relevant_experience_years": 0,
    "education_level_code": 3
}
---------------------------------------------------

-----------------Resume Text------------------
Rahul Sharma Email: <EMAIL> Phone: +91 9876543210 Location: Vijayawada, Andhra Pradesh Summary: Recent Computer Science graduate with strong foundation in Java programming, MySQL database management, and Git version control. Passionate about building scalable software solutions and eager to contribute to innovative development projects.Education: Bachelor of Technology in Computer Science, VIT University, Vellore (2024) - CGPA: 8.2/10 Skills: Core Java, MySQL, Git, Data Structures, Algorithms, OOPs, Exception Handling, Collections Framework, HTML/CSS, JavaScriptProjects: - Library Management System: Developed a comprehensive library management system using Java and MySQL with features for book inventory, member management, and transaction tracking. - Student Information System: Created a web-based student information system with user authentication, grade management, and reporting features. Experience: Software Development Intern, TechCorp Solutions (Jun 2023 - Aug 2023)  Assisted in developing backend APIs using Java  Worked with MySQL database for data management  Participated in code reviews and testing activities  Collaborated with team using Git for version control
---------------------------------------------------

-----------------RAW LLM RESPONSE------------------
No professional experience found.
---------------------------------------------------

-----------------Cleaned Experience------------------
No professional experience found.
---------------------------------------------------

-----------------RAW LLM RESPONSE------------------
{
    "candidate_name": "Rahul Sharma",
    "overall_score": 75,
    "skills_match": 60,
    "experience_relevance": 80,
    "education": 85,
    "keywords_match": 70,
    "overall_fit": 78,
    "matching_skills": ["HTML/CSS", "JavaScript", "Git"],
    "missing_skills": ["React.js or Vue.js", "Responsive design principles", "Cross-browser compatibility issues"],
    "matching_experience": ["Software Development Intern, TechCorp Solutions (Jun 2023 - Aug 2023)"],
    "experience_gaps": [],
    "education_highlights": ["Bachelor of Technology in Computer Science, VIT University, Vellore (2024) - CGPA: 8.2/10"],
    "strengths": ["Strong foundation in Java programming", "MySQL database management", "Git version control"],
    "weaknesses": ["Limited experience with frontend frameworks and responsive design principles"],
    "growth_potential": 80,
    "cultural_fit_indicators": [],
    "salary_expectation_alignment": "MEDIUM",
    "recommendation": "CONSIDER",
    "recommendation_reason": "Candidate has a strong foundation in Java programming, but lacks experience with frontend frameworks and responsive design principles.",
    "summary": "Recent Computer Science graduate with strong foundation in Java programming, MySQL database management, and Git version control. Passionate about building scalable software solutions and eager to contribute to innovative development projects.",
    "interview_focus_areas": ["Frontend framework knowledge", "Responsive design principles"],
    "matching_skills_count": 3,
    "missing_skills_count": 3,
    "relevant_experience_years": 1,
    "education_level_code": 2
}
---------------------------------------------------
