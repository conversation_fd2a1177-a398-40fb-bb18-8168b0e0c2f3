# Ngrok Setup Guide for Django E-commerce Project

This guide helps you expose your Django development server to the internet using ngrok for testing webhooks, external APIs, and sharing your application.

## 🚀 Quick Start

### Method 1: Automated Setup (Recommended)

**Windows:**
```bash
# Run the batch script
scripts\start_ngrok.bat
```

**Linux/macOS:**
```bash
# Make script executable and run
chmod +x scripts/start_ngrok.sh
./scripts/start_ngrok.sh
```

### Method 2: Django Management Command

```bash
# Start ngrok tunnel
python manage.py start_ngrok

# With custom options
python manage.py start_ngrok --port 8000 --region us
```

### Method 3: Manual Setup

```bash
# Start ngrok tunnel manually
ngrok http 8000

# Then set environment variables
export NGROK_DOMAIN=your-ngrok-domain.ngrok-free.app
export NGROK_ENABLED=true

# Start Django server
python manage.py runserver
```

## 📋 Prerequisites

### 1. Install Ngrok

**Windows (Chocolatey):**
```bash
choco install ngrok
```

**macOS (Homebrew):**
```bash
brew install ngrok/ngrok/ngrok
```

**Linux (Snap):**
```bash
snap install ngrok
```

**Manual Installation:**
1. Go to [https://ngrok.com/download](https://ngrok.com/download)
2. Download for your OS
3. Extract and add to PATH

### 2. Get Ngrok Auth Token

1. Sign up at [https://ngrok.com/](https://ngrok.com/)
2. Go to [https://dashboard.ngrok.com/get-started/your-authtoken](https://dashboard.ngrok.com/get-started/your-authtoken)
3. Copy your auth token
4. Configure it:
```bash
ngrok config add-authtoken YOUR_AUTH_TOKEN_HERE
```

## 🔧 Configuration

### Environment Variables

The setup automatically creates these environment variables:

```bash
NGROK_ENABLED=true
NGROK_DOMAIN=your-unique-domain.ngrok-free.app
```

### Django Settings

Your `settings.py` is automatically configured to:
- Add ngrok domain to `ALLOWED_HOSTS`
- Enable ngrok mode when `NGROK_ENABLED=true`
- Support dynamic domain configuration

## 📱 Usage Examples

### Testing Payment Webhooks

**Stripe Webhooks:**
```bash
# Your webhook URL becomes:
https://your-domain.ngrok-free.app/stripe/webhook/
```

**PhonePe Webhooks:**
```bash
# Your callback URL becomes:
https://your-domain.ngrok-free.app/phonepe/callback/
```

### Testing External APIs

**Face Recognition API:**
```bash
# External services can now access:
https://your-domain.ngrok-free.app/get_res/
https://your-domain.ngrok-free.app/register_face/save_frame/
```

### WebSocket Testing

**Resume Analysis WebSocket:**
```bash
# WebSocket URL becomes:
wss://your-domain.ngrok-free.app/ws/resume-analysis/1/
```

## 🛠️ Advanced Configuration

### Custom Subdomain (Ngrok Pro)

```bash
python manage.py start_ngrok --subdomain myapp
# Results in: https://myapp.ngrok.io
```

### Different Region

```bash
python manage.py start_ngrok --region eu
# Available: us, eu, ap, au, sa, jp, in
```

### Custom Port

```bash
python manage.py start_ngrok --port 8080
```

## 🔍 Troubleshooting

### Common Issues

**1. "Authentication failed"**
```bash
# Solution: Configure auth token
ngrok config add-authtoken YOUR_TOKEN
```

**2. "Tunnel not found"**
```bash
# Solution: Wait a few seconds after starting ngrok
# Or check if ngrok is running: curl http://localhost:4040/api/tunnels
```

**3. "ALLOWED_HOSTS error"**
```bash
# Solution: Restart Django after ngrok starts
# Or manually add domain to ALLOWED_HOSTS
```

**4. "WebSocket connection failed"**
```bash
# Solution: Use wss:// for HTTPS ngrok URLs
# Check if Daphne is running for WebSocket support
```

### Debug Commands

```bash
# Check ngrok status
curl http://localhost:4040/api/tunnels

# Check ngrok web interface
# Open: http://localhost:4040

# Test tunnel connectivity
curl https://your-domain.ngrok-free.app/

# Check Django logs
python manage.py runserver --verbosity=2
```

## 📊 Monitoring

### Ngrok Web Interface

Access the ngrok web interface at: [http://localhost:4040](http://localhost:4040)

Features:
- Request/response inspection
- Replay requests
- Traffic statistics
- Tunnel status

### Django Logs

Monitor Django logs for ngrok requests:
```bash
# Enable verbose logging
python manage.py runserver --verbosity=2

# Or check specific logs
tail -f django.log
```

## 🔒 Security Considerations

### Free Ngrok Limitations

- URLs change on restart
- Limited to 1 tunnel
- No custom domains
- Basic authentication only

### Production Alternatives

For production use, consider:
- **Ngrok Pro/Business**: Custom domains, multiple tunnels
- **Cloudflare Tunnel**: Free alternative with custom domains
- **AWS Application Load Balancer**: For AWS deployments
- **Nginx Reverse Proxy**: For VPS deployments

### Security Best Practices

1. **Never expose production databases**
2. **Use environment-specific settings**
3. **Enable HTTPS only in production**
4. **Limit tunnel access time**
5. **Monitor tunnel usage**

## 🎯 Use Cases

### Development & Testing

- **Webhook Testing**: Payment gateways, third-party APIs
- **Mobile App Testing**: Test API endpoints from mobile devices
- **Client Demos**: Share work-in-progress with clients
- **Cross-device Testing**: Test on different devices/browsers

### Integration Testing

- **Payment Gateway Integration**: Test Stripe, PhonePe, PayPal
- **Social Login**: Test OAuth with Google, Facebook
- **Email Services**: Test email delivery webhooks
- **SMS Services**: Test SMS delivery callbacks

### Collaboration

- **Team Testing**: Share development environment
- **QA Testing**: Allow QA team to test features
- **Stakeholder Reviews**: Demo features to stakeholders
- **Bug Reproduction**: Share exact environment for debugging

## 📞 Support

If you encounter issues:

1. Check the [troubleshooting section](#troubleshooting)
2. Review ngrok logs at [http://localhost:4040](http://localhost:4040)
3. Check Django logs for errors
4. Verify environment variables are set correctly

## 🔄 Updates

To update this setup:

1. **Update ngrok**: Download latest version from ngrok.com
2. **Update scripts**: Pull latest changes from repository
3. **Update dependencies**: `pip install -r requirements.txt`

---

**Happy Testing! 🚀**
