@echo off
REM Django Ngrok Startup Script for Windows
REM This script starts both Django server and ngrok tunnel

echo ========================================
echo Django E-commerce Ngrok Setup
echo ========================================

REM Check if Python is available
python --version >nul 2>&1
if errorlevel 1 (
    echo ERROR: Python is not installed or not in PATH
    echo Please install Python and try again
    pause
    exit /b 1
)

REM Check if ngrok is available
ngrok version >nul 2>&1
if errorlevel 1 (
    echo ERROR: <PERSON>rok is not installed or not in PATH
    echo.
    echo To install ngrok:
    echo 1. Go to https://ngrok.com/download
    echo 2. Download and extract ngrok.exe
    echo 3. Add ngrok.exe to your PATH
    echo 4. Get auth token from https://dashboard.ngrok.com/
    echo 5. Run: ngrok config add-authtoken YOUR_TOKEN
    echo.
    pause
    exit /b 1
)

echo ✅ Python and ngrok are available

REM Navigate to project directory
cd /d "%~dp0.."

REM Activate virtual environment if it exists
if exist "venv\Scripts\activate.bat" (
    echo 🔧 Activating virtual environment...
    call venv\Scripts\activate.bat
) else if exist "env\Scripts\activate.bat" (
    echo 🔧 Activating virtual environment...
    call env\Scripts\activate.bat
) else (
    echo ⚠️  No virtual environment found, using system Python
)

REM Install required packages if needed
echo 🔧 Checking dependencies...
pip install requests >nul 2>&1

REM Run the ngrok setup script
echo 🚀 Starting ngrok setup...
python scripts\setup_ngrok.py

if errorlevel 1 (
    echo ❌ Ngrok setup failed
    pause
    exit /b 1
)

echo.
echo 🎉 Setup complete! Check the output above for your public URL.
echo.
pause
