#!/bin/bash
# Django Ngrok Startup Script for Linux/macOS
# This script starts both Django server and ngrok tunnel

echo "========================================"
echo "Django E-commerce Ngrok Setup"
echo "========================================"

# Check if Python is available
if ! command -v python3 &> /dev/null && ! command -v python &> /dev/null; then
    echo "❌ ERROR: Python is not installed or not in PATH"
    echo "Please install Python and try again"
    exit 1
fi

# Use python3 if available, otherwise python
PYTHON_CMD="python3"
if ! command -v python3 &> /dev/null; then
    PYTHON_CMD="python"
fi

# Check if ngrok is available
if ! command -v ngrok &> /dev/null; then
    echo "❌ ERROR: Ngrok is not installed or not in PATH"
    echo ""
    echo "To install ngrok:"
    echo "1. Go to https://ngrok.com/download"
    echo "2. Download and extract ngrok"
    echo "3. Add ngrok to your PATH"
    echo "4. Get auth token from https://dashboard.ngrok.com/"
    echo "5. Run: ngrok config add-authtoken YOUR_TOKEN"
    echo ""
    echo "Or install via package manager:"
    echo "- macOS: brew install ngrok/ngrok/ngrok"
    echo "- Linux: snap install ngrok"
    exit 1
fi

echo "✅ Python and ngrok are available"

# Navigate to project directory
cd "$(dirname "$0")/.."

# Activate virtual environment if it exists
if [ -f "venv/bin/activate" ]; then
    echo "🔧 Activating virtual environment..."
    source venv/bin/activate
elif [ -f "env/bin/activate" ]; then
    echo "🔧 Activating virtual environment..."
    source env/bin/activate
else
    echo "⚠️  No virtual environment found, using system Python"
fi

# Install required packages if needed
echo "🔧 Checking dependencies..."
$PYTHON_CMD -m pip install requests > /dev/null 2>&1

# Make the Python script executable
chmod +x scripts/setup_ngrok.py

# Run the ngrok setup script
echo "🚀 Starting ngrok setup..."
$PYTHON_CMD scripts/setup_ngrok.py

if [ $? -ne 0 ]; then
    echo "❌ Ngrok setup failed"
    exit 1
fi

echo ""
echo "🎉 Setup complete! Check the output above for your public URL."
echo ""
