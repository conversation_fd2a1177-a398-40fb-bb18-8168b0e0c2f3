{% load static %}

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WebSocket Resume Analysis</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.0/font/bootstrap-icons.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.7.0.min.js"></script>
    <script>
        $(document).ready( () => {
            let socket = null;
            let reconnectAttempts = 0;
            const maxReconnectAttempts = 5;
            let reconnectInterval = null;
            let isAnalysisRunning = false;

            // Function to connect/reconnect WebSocket
            function connectWebSocket() {
                try {
                    // Close existing socket if it exists
                    if (socket && socket.readyState !== WebSocket.CLOSED) {
                        socket.close();
                    }

                    socket = new WebSocket('ws://127.0.0.1:8000/ws/test/');

                    socket.onopen = function(event) {
                        console.log('WebSocket connection established.');
                        reconnectAttempts = 0; // Reset attempts on successful connection
                        updateConnectionStatus(true);

                        // Clear any existing reconnect interval
                        if (reconnectInterval) {
                            clearInterval(reconnectInterval);
                            reconnectInterval = null;
                        }

                        // Check for ongoing analysis when connection opens
                        try {
                            socket.send(JSON.stringify({ check_status: true }));
                        } catch (sendError) {
                            console.error('Error sending status check:', sendError);
                        }
                    };

                    socket.onmessage = handleWebSocketMessage;

                    socket.onclose = function(event) {
                        console.log('WebSocket connection closed.', event);
                        updateConnectionStatus(false);

                        // Only attempt reconnect if analysis is running or if it wasn't a clean close
                        if (isAnalysisRunning || event.code !== 1000) {
                            attemptReconnect();
                        }
                    };

                    socket.onerror = function(error) {
                        console.error('WebSocket error: ', error);
                        updateConnectionStatus(false);
                        socket = null; // Set to null on error

                        // Don't immediately reconnect on error, let onclose handle it
                    };

                } catch (error) {
                    console.error('Failed to create WebSocket:', error);
                    socket = null;
                    updateConnectionStatus(false);
                    attemptReconnect();
                }
            }

            // Function to update connection status in UI
            function updateConnectionStatus(connected) {
                if (connected) {
                    $('#connectionStatus').html('<span class="badge bg-success">🟢 Connected</span>');
                } else {
                    $('#connectionStatus').html('<span class="badge bg-danger">🔴 Disconnected</span>');
                }
            }

            // Function to attempt reconnection
            function attemptReconnect() {
                if (reconnectAttempts < maxReconnectAttempts && !reconnectInterval) {
                    reconnectAttempts++;
                    console.log(`Attempting to reconnect (${reconnectAttempts}/${maxReconnectAttempts})`);

                    $('#progressInfo').append(`
                        <div class="alert alert-warning">
                            <i class="bi bi-wifi-off"></i> Connection lost. Reconnecting... (${reconnectAttempts}/${maxReconnectAttempts})
                        </div>
                    `);

                    reconnectInterval = setTimeout(() => {
                        reconnectInterval = null;
                        connectWebSocket();
                    }, 2000 * reconnectAttempts); // Exponential backoff
                } else if (reconnectAttempts >= maxReconnectAttempts) {
                    $('#progressInfo').append(`
                        <div class="alert alert-danger">
                            <i class="bi bi-exclamation-triangle"></i> Failed to reconnect after ${maxReconnectAttempts} attempts. Please refresh the page.
                        </div>
                    `);
                }
            }

            // Function to handle WebSocket messages
            function handleWebSocketMessage(event) {
                console.log('Message from server ', event.data);

                try {
                    const data = JSON.parse(event.data);

                    // Handle reconnection to ongoing analysis
                    if (data.reconnected_to_analysis) {
                        isAnalysisRunning = true;
                        const progress = data.progress || {};
                        const completedBatches = progress.completed_batches || 0;
                        const totalBatches = progress.total_batches || 0;

                        // Show bottom-right popup
                        showProgressPopup(`Reconnected! Progress: ${completedBatches}/${totalBatches}`, 'warning');

                        // Update button to show analysis is running
                        $('#openModalBtn').html('<i class="bi bi-hourglass-split"></i> Analysis Running').prop('disabled', true);

                        console.log('Reconnected to ongoing analysis:', data);
                    }

                    // Handle analysis started
                    if (data.analysis_started) {
                        isAnalysisRunning = true;
                        showProgressPopup('Analysis started successfully!', 'info');

                        // Update button to show analysis is running
                        $('#openModalBtn').html('<i class="bi bi-hourglass-split"></i> Analysis Running').prop('disabled', true);
                    }

                    // Handle batch completion
                    if (data.batch_analyse_success) {
                        const batchNo = data.batch_no;
                        const jobCode = data.job_code;
                        const results = data.results;

                        // Get total batches from cache or estimate
                        const totalBatches = data.total_batches || Math.ceil(data.total_resumes / 2) || '?';

                        // Show progress popup with batch numbers
                        showProgressPopup(`Batch ${batchNo}/${totalBatches} completed! ✅`, 'success');

                        // Auto-hide batch completion popup after 2 seconds
                        setTimeout(() => {
                            hideProgressPopup();
                        }, 2000);

                        // Display results in table format
                        console.log('Batch results received:', results);
                        if (results) {
                            displayBatchResults(batchNo, jobCode, results);
                        } else {
                            console.log('No results data received for batch', batchNo);
                        }
                    }

                    // Handle analysis completion
                    if (data.analysis_completed) {
                        isAnalysisRunning = false;
                        showProgressPopup('All analysis completed! ✅', 'success');

                        // Reset button to normal state
                        $('#openModalBtn').html('<i class="bi bi-play-circle"></i> New Analysis').prop('disabled', false);

                        // Hide progress popup after 3 seconds
                        setTimeout(() => {
                            hideProgressPopup();
                        }, 3000);
                    }

                    // Handle analysis errors
                    if (data.error) {
                        isAnalysisRunning = false;
                        showProgressPopup(`Analysis failed: ${data.message}`, 'error');

                        // Reset button to normal state
                        $('#openModalBtn').html('<i class="bi bi-play-circle"></i> New Analysis').prop('disabled', false);

                        // Hide progress popup after 5 seconds for errors
                        setTimeout(() => {
                            hideProgressPopup();
                        }, 5000);
                    }
                } catch (error) {
                    console.error('Error parsing WebSocket message:', error);
                }
            }




            $("#testBtn").click(() => {
                if (socket && socket.readyState === WebSocket.OPEN) {
                    socket.send('');
                } else {
                    console.log('WebSocket not connected');
                }
            });

            $('#openModalBtn').click(function() {
                // Check if WebSocket is connected
                if (!socket || socket.readyState !== WebSocket.OPEN) {
                    showProgressPopup('WebSocket not connected. Attempting to reconnect...', 'warning');
                    // Try to reconnect
                    if (!socket) {
                        connectWebSocket();
                    }
                    return;
                }

                // Check if analysis is already running
                if (isAnalysisRunning) {
                    showProgressPopup('Analysis already in progress! Please wait...', 'warning');
                    return;
                }

                // Clear previous results
                $('#resultsContainer').html('');
                $('#resultsSection').hide();

                // Show initial progress popup
                showProgressPopup('Analysis started! Processing resumes in batches...', 'info');

                // Set analysis as running and update button
                isAnalysisRunning = true;
                $('#openModalBtn').html('<i class="bi bi-hourglass-split"></i> Analysis Running').prop('disabled', true);

                const data = {
                    start_analysis: true,
                    job_code: 'FE01',
                    job_id: '15'
                }

                try {
                    socket.send(JSON.stringify(data));
                } catch (error) {
                    console.error('Error sending data:', error);
                    showProgressPopup('Failed to send data. Connection may be lost. Reconnecting...', 'error');
                    // Try to reconnect
                    connectWebSocket();
                }
            });

            // Function to show bottom-right progress popup
            function showProgressPopup(message, type = 'info') {
                const popup = $('#progressPopup');
                const alertClass = type === 'success' ? 'alert-success' :
                                 type === 'warning' ? 'alert-warning' :
                                 type === 'error' ? 'alert-danger' : 'alert-info';

                popup.removeClass('alert-info alert-success alert-warning alert-danger')
                     .addClass(alertClass)
                     .html(`<i class="bi bi-activity"></i> ${message}`)
                     .fadeIn();
            }

            // Function to hide progress popup
            function hideProgressPopup() {
                $('#progressPopup').fadeOut();
            }

            // Function to display batch results in table format
            function displayBatchResults(batchNo, jobCode, results) {
                console.log('displayBatchResults called with:', {batchNo, jobCode, results});
                console.log('Results type:', typeof results);
                console.log('Results is array:', Array.isArray(results));

                let tableHtml = `
                    <div class="card mt-3">
                        <div class="card-header bg-primary text-white">
                            <h6 class="mb-0"><i class="bi bi-table"></i> Batch ${batchNo} Results - Job Code: ${jobCode}</h6>
                        </div>
                        <div class="card-body p-0">
                            <div class="table-responsive">
                                <table class="table table-striped table-hover mb-0">
                                    <thead class="table-dark">
                                        <tr>
                                            <th>Candidate Name</th>
                                            <th>Email</th>
                                            <th>Phone</th>
                                            <th>Final Score</th>
                                            <th>Skills Match</th>
                                            <th>Experience</th>
                                            <th>Recommendation</th>
                                            <th>Strengths</th>
                                        </tr>
                                    </thead>
                                    <tbody>`;

                // Add rows for each candidate result - results is an array of candidates
                if (results && Array.isArray(results) && results.length > 0) {
                    results.forEach(candidate => {
                        const finalScore = candidate.scores?.final_score || candidate.final_score || 0;
                        const skillsMatch = candidate.scores?.skills_match || 0;
                        const experienceScore = candidate.scores?.experience_score || 0;
                        const recommendation = candidate.recommendation?.decision || candidate.recommendation || 'N/A';

                        const scoreClass = finalScore >= 80 ? 'text-success fw-bold' :
                                         finalScore >= 60 ? 'text-warning fw-bold' : 'text-danger fw-bold';

                        const recClass = recommendation === 'HIRE' ? 'bg-success' :
                                       recommendation === 'CONSIDER' ? 'bg-warning' : 'bg-danger';

                        tableHtml += `
                            <tr>
                                <td><strong>${candidate.candidate_name || 'N/A'}</strong></td>
                                <td>${candidate.email || 'N/A'}</td>
                                <td>${candidate.contact_number || 'N/A'}</td>
                                <td><span class="${scoreClass}">${finalScore.toFixed(1)}%</span></td>
                                <td><span class="badge bg-info">${skillsMatch.toFixed(1)}%</span></td>
                                <td><span class="badge bg-secondary">${experienceScore.toFixed(1)}%</span></td>
                                <td>
                                    <span class="badge ${recClass}">
                                        ${recommendation}
                                    </span>
                                </td>
                                <td>
                                    <div class="d-flex flex-wrap gap-1">
                                        ${(candidate.strengths || []).slice(0, 2).map(strength =>
                                            `<span class="badge bg-success" title="${strength}">${strength.length > 20 ? strength.substring(0, 20) + '...' : strength}</span>`
                                        ).join('')}
                                        ${(candidate.strengths || []).length > 2 ?
                                            `<span class="badge bg-light text-dark">+${(candidate.strengths || []).length - 2}</span>` : ''}
                                    </div>
                                </td>
                            </tr>`;
                    });
                } else {
                    tableHtml += `
                        <tr>
                            <td colspan="8" class="text-center text-muted py-3">
                                <i class="bi bi-info-circle"></i> No candidate data available for this batch
                            </td>
                        </tr>`;
                }

                tableHtml += `
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>`;

                // Show results section if it's hidden
                $('#resultsSection').show();

                $('#resultsContainer').append(tableHtml);

                // Scroll to the new results table
                setTimeout(() => {
                    const newTable = $('#resultsContainer .card').last();
                    if (newTable.length) {
                        $('html, body').animate({
                            scrollTop: newTable.offset().top - 100
                        }, 500);
                    }
                }, 100);
            }

            // Initialize WebSocket connection
            connectWebSocket();
        });
    </script>
</head>
<body>
    <div class="container mt-4" id="container">
  <h2 class="mb-4 fw-bold text-primary d-flex justify-content-between">📂 Resume Analysis
    <div class="d-flex align-items-center gap-2">
      <span id="connectionStatus">
        <span class="badge bg-secondary">🟡 Connecting...</span>
      </span>
      <button class="btn btn-primary btn-sm" id="openModalBtn">New Analysis</button>
    </div>
  </h2>

  {% if output_file_names %}
    <div class="row row-cols-1 row-cols-md-2 row-cols-lg-3 g-4">
      {% for filename in output_file_names %}
        <div class="col">
          <div class="card shadow-sm h-100 border-0 rounded-3">
            <div class="card-body bg-light d-flex flex-column justify-content-center align-items-start">
              <h5 class="card-title fw-semibold text-dark mb-2">
                <i class="bi bi-file-earmark-text-fill me-2"></i> {{ filename }}
              </h5>
              <a href="{{request.path}}/{{filename}}" class="btn btn-outline-primary btn-sm mt-auto">View Details</a>
            </div>
          </div>
        </div>
      {% endfor %}
    </div>
  {% else %}
    <div class="alert alert-info">No output files found.</div>
  {% endif %}
</div>

<!-- Results Container for Batch Analysis -->
<div class="container mt-4">
    <div id="resultsSection" style="display: none;">
        <div class="d-flex align-items-center mb-3">
            <h4 class="mb-0 text-primary">
                <i class="bi bi-table"></i> Analysis Results
            </h4>
            <div class="ms-auto">
                <button class="btn btn-outline-secondary btn-sm" onclick="$('#resultsContainer').html(''); $('#resultsSection').hide();">
                    <i class="bi bi-trash"></i> Clear Results
                </button>
            </div>
        </div>
    </div>
    <div id="resultsContainer">
        <!-- Batch results tables will be added here -->
    </div>
</div>

<!-- Modal -->
<div class="modal fade" id="myModal" tabindex="-1" aria-labelledby="myModalLabel" aria-hidden="true">
  <div class="modal-dialog modal-lg">
    <div class="modal-content border-0 shadow">
      <div class="modal-header bg-primary text-white">
        <h5 class="modal-title mb-0" id="myModalLabel">
          <i class="bi bi-activity"></i> Resume Analysis in Progress
        </h5>
        <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
      </div>
      <div class="modal-body py-4">
        <!-- Loading Spinner -->
        <div class="text-center mb-4">
          <div class="spinner-border text-primary mb-3" role="status">
            <span class="visually-hidden">Loading...</span>
          </div>
          <p class="fw-semibold mb-0">
            Processing resumes in batches of 2...<br>
            <small class="text-muted">Real-time updates will appear below</small>
          </p>
        </div>

        <!-- Modal content (not used for results anymore) -->
        <div class="text-center">
          <p class="text-muted">Analysis results will appear below the main content.</p>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Bottom-right progress popup -->
<div id="progressPopup" class="alert alert-info position-fixed" style="
    bottom: 20px;
    right: 20px;
    z-index: 9999;
    min-width: 300px;
    max-width: 400px;
    display: none;
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
    border-radius: 8px;
">
    <i class="bi bi-activity"></i> Analysis in progress...
</div>

<style>
    #progressPopup {
        animation: slideInRight 0.3s ease-out;
    }

    @keyframes slideInRight {
        from {
            transform: translateX(100%);
            opacity: 0;
        }
        to {
            transform: translateX(0);
            opacity: 1;
        }
    }

    .table th {
        font-size: 0.9rem;
        font-weight: 600;
    }

    .table td {
        font-size: 0.85rem;
        vertical-align: middle;
    }

    .badge {
        font-size: 0.7rem;
    }
</style>

</body>
</html>