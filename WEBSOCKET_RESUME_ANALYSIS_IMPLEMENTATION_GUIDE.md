# WebSocket-Based Real-Time Resume Analysis Implementation Guide

## Overview
This guide provides a complete step-by-step implementation for converting your synchronous Django resume analysis system to use WebSockets for real-time batch processing with live frontend updates.

## Current System Analysis
- **Models**: `CreateJob`, `Apply<PERSON>ob`, `ResumeAnalysis`, `ResumeAttributes`
- **Current Flow**: Synchronous processing via `analyse_resumes` view → `process_resumes` function → `ResumeAnalyzer.analyze_multiple_resumes`
- **Frontend**: AJAX call with modal showing "processing" until completion
- **Batch Size**: Currently processes all resumes at once

## Target Architecture
- **ASGI Server**: Daphne (handles both HTTP and WebSocket connections)
- **Backend**: Django Channels + WebSocket consumers + Background tasks
- **Frontend**: WebSocket connection + Real-time UI updates
- **Processing**: Batch processing (2 resumes at a time) with progress updates
- **User Experience**: Live progress tracking without page refresh

---

## Step 1: Install Dependencies

### 1.1 Update requirements.txt
```txt
# Add these to your existing requirement.txt
channels==4.0.0
channels-redis==4.1.0
redis==5.0.1
asgiref==3.7.2
daphne==4.0.0
```

### 1.2 Install packages
```bash
pip install channels==4.0.0 channels-redis==4.1.0 redis==5.0.1 daphne==4.0.0
```

---

## Step 2: Configure Django Channels

### 2.1 Update ecommerce/settings.py
```python
# Add to INSTALLED_APPS (add 'daphne' FIRST, then 'channels')
INSTALLED_APPS = [
    'daphne',  # Add this FIRST - enables Daphne for runserver
    'django.contrib.admin',
    'django.contrib.auth',
    'django.contrib.contenttypes',
    'django.contrib.sessions',
    'django.contrib.messages',
    'django.contrib.staticfiles',
    'ecom',
    'widget_tweaks',
    'paypal.standard.ipn',
    'django_flatpickr',
    'channels',  # Add this after daphne
]

# Add at the end of settings.py
ASGI_APPLICATION = 'ecommerce.asgi.application'

# Channel layers configuration
CHANNEL_LAYERS = {
    'default': {
        'BACKEND': 'channels_redis.core.RedisChannelLayer',
        'CONFIG': {
            "hosts": [(settings.LOCALHOST_IP, 6379)],
        },
    },
}

# Resume analysis settings
RESUME_BATCH_SIZE = 2  # Process 2 resumes at a time
RESUME_ANALYSIS_TIMEOUT = 300  # 5 minutes timeout per batch
```

### 2.2 Create ecommerce/asgi.py
```python
import os
from django.core.asgi import get_asgi_application
from channels.routing import ProtocolTypeRouter, URLRouter
from channels.auth import AuthMiddlewareStack
from channels.security.websocket import AllowedHostsOriginValidator
import ecom.routing

os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'ecommerce.settings')

application = ProtocolTypeRouter({
    "http": get_asgi_application(),
    "websocket": AllowedHostsOriginValidator(
        AuthMiddlewareStack(
            URLRouter(
                ecom.routing.websocket_urlpatterns
            )
        )
    ),
})
```

---

## Step 3: Create Database Models for Batch Tracking

### 3.1 Add models to ecom/models.py
```python
import uuid
from django.db import models
from django.utils import timezone

class ResumeAnalysisBatch(models.Model):
    """Track batch processing of resume analysis"""
    
    STATUS_CHOICES = [
        ('PENDING', 'Pending'),
        ('PROCESSING', 'Processing'),
        ('COMPLETED', 'Completed'),
        ('FAILED', 'Failed'),
    ]
    
    batch_id = models.UUIDField(default=uuid.uuid4, unique=True, editable=False)
    job = models.ForeignKey(CreateJob, on_delete=models.CASCADE, related_name='analysis_batches')
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='PENDING')
    total_resumes = models.IntegerField(default=0)
    processed_resumes = models.IntegerField(default=0)
    successful_resumes = models.IntegerField(default=0)
    failed_resumes = models.IntegerField(default=0)
    created_at = models.DateTimeField(auto_now_add=True)
    started_at = models.DateTimeField(null=True, blank=True)
    completed_at = models.DateTimeField(null=True, blank=True)
    error_message = models.TextField(blank=True)
    
    def progress_percentage(self):
        if self.total_resumes == 0:
            return 0
        return round((self.processed_resumes / self.total_resumes) * 100, 1)
    
    def estimated_time_remaining(self):
        if self.processed_resumes == 0 or not self.started_at:
            return None
        
        elapsed = (timezone.now() - self.started_at).total_seconds()
        avg_time_per_resume = elapsed / self.processed_resumes
        remaining_resumes = self.total_resumes - self.processed_resumes
        return round(remaining_resumes * avg_time_per_resume)
    
    def __str__(self):
        return f"Batch {self.batch_id} - {self.job.job_code} ({self.status})"

class ResumeProcessingStatus(models.Model):
    """Track individual resume processing within a batch"""
    
    STATUS_CHOICES = [
        ('PENDING', 'Pending'),
        ('PROCESSING', 'Processing'),
        ('COMPLETED', 'Completed'),
        ('FAILED', 'Failed'),
    ]
    
    batch = models.ForeignKey(ResumeAnalysisBatch, on_delete=models.CASCADE, related_name='resume_statuses')
    application = models.ForeignKey(ApplyJob, on_delete=models.CASCADE)
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='PENDING')
    started_at = models.DateTimeField(null=True, blank=True)
    completed_at = models.DateTimeField(null=True, blank=True)
    error_message = models.TextField(blank=True)
    processing_time = models.FloatField(null=True, blank=True)  # seconds
    
    class Meta:
        unique_together = ['batch', 'application']
    
    def __str__(self):
        return f"{self.application.name} - {self.status}"
```

### 3.2 Create and run migrations
```bash
python manage.py makemigrations
python manage.py migrate
```

---

## Step 4: Create WebSocket Consumer

### 4.1 Create ecom/consumers.py
```python
import json
import asyncio
import logging
from channels.generic.websocket import AsyncWebsocketConsumer
from channels.db import database_sync_to_async
from django.contrib.auth.models import AnonymousUser
from .models import CreateJob, ApplyJob, ResumeAnalysisBatch, ResumeProcessingStatus
from .tasks import start_batch_analysis

logger = logging.getLogger(__name__)

class ResumeAnalysisConsumer(AsyncWebsocketConsumer):
    async def connect(self):
        # Check if user is authenticated
        if self.scope["user"] == AnonymousUser():
            await self.close()
            return
            
        self.job_id = self.scope['url_route']['kwargs']['job_id']
        self.room_group_name = f'resume_analysis_{self.job_id}'
        
        # Join room group
        await self.channel_layer.group_add(
            self.room_group_name,
            self.channel_name
        )
        
        await self.accept()
        logger.info(f"WebSocket connected for job {self.job_id}")

    async def disconnect(self, close_code):
        # Leave room group
        await self.channel_layer.group_discard(
            self.room_group_name,
            self.channel_name
        )
        logger.info(f"WebSocket disconnected for job {self.job_id}")

    async def receive(self, text_data):
        try:
            data = json.loads(text_data)
            action = data.get('action')
            
            if action == 'start_analysis':
                await self.start_analysis()
            elif action == 'get_status':
                batch_id = data.get('batch_id')
                if batch_id:
                    await self.send_batch_status(batch_id)
                    
        except json.JSONDecodeError:
            await self.send(text_data=json.dumps({
                'type': 'error',
                'message': 'Invalid JSON data'
            }))
        except Exception as e:
            logger.error(f"Error in receive: {str(e)}")
            await self.send(text_data=json.dumps({
                'type': 'error',
                'message': 'Internal server error'
            }))

    async def start_analysis(self):
        """Start the batch analysis process"""
        try:
            # Get job and resumes
            job = await database_sync_to_async(CreateJob.objects.get)(id=self.job_id)
            resumes = await database_sync_to_async(list)(
                ApplyJob.objects.filter(job_code=job.job_code, analysed=0)
            )
            
            if not resumes:
                await self.send(text_data=json.dumps({
                    'type': 'error',
                    'message': 'No unanalyzed resumes found for this job'
                }))
                return
            
            # Create batch record
            batch = await database_sync_to_async(ResumeAnalysisBatch.objects.create)(
                job=job,
                total_resumes=len(resumes),
                status='PENDING'
            )
            
            # Create individual resume status records
            for resume in resumes:
                await database_sync_to_async(ResumeProcessingStatus.objects.create)(
                    batch=batch,
                    application=resume,
                    status='PENDING'
                )
            
            # Send initial status
            await self.send(text_data=json.dumps({
                'type': 'analysis_started',
                'batch_id': str(batch.batch_id),
                'total_resumes': batch.total_resumes,
                'message': f'Analysis started for {batch.total_resumes} resumes'
            }))
            
            # Start background processing
            await database_sync_to_async(start_batch_analysis.delay)(str(batch.batch_id))
            
        except CreateJob.DoesNotExist:
            await self.send(text_data=json.dumps({
                'type': 'error',
                'message': 'Job not found'
            }))
        except Exception as e:
            logger.error(f"Error starting analysis: {str(e)}")
            await self.send(text_data=json.dumps({
                'type': 'error',
                'message': f'Failed to start analysis: {str(e)}'
            }))

    async def send_batch_status(self, batch_id):
        """Send current batch status"""
        try:
            batch = await database_sync_to_async(
                ResumeAnalysisBatch.objects.get
            )(batch_id=batch_id)
            
            await self.send(text_data=json.dumps({
                'type': 'batch_status',
                'batch_id': str(batch.batch_id),
                'status': batch.status,
                'total_resumes': batch.total_resumes,
                'processed_resumes': batch.processed_resumes,
                'successful_resumes': batch.successful_resumes,
                'failed_resumes': batch.failed_resumes,
                'progress_percentage': batch.progress_percentage(),
                'estimated_time_remaining': batch.estimated_time_remaining()
            }))
            
        except ResumeAnalysisBatch.DoesNotExist:
            await self.send(text_data=json.dumps({
                'type': 'error',
                'message': 'Batch not found'
            }))

    # Handler for messages sent to the group
    async def batch_update(self, event):
        """Send batch update to WebSocket"""
        await self.send(text_data=json.dumps(event['data']))
    
    async def resume_completed(self, event):
        """Send individual resume completion to WebSocket"""
        await self.send(text_data=json.dumps(event['data']))
    
    async def analysis_completed(self, event):
        """Send analysis completion to WebSocket"""
        await self.send(text_data=json.dumps(event['data']))
    
    async def analysis_error(self, event):
        """Send analysis error to WebSocket"""
        await self.send(text_data=json.dumps(event['data']))
```

### 4.2 Create ecom/routing.py
```python
from django.urls import re_path
from . import consumers

websocket_urlpatterns = [
    re_path(r'ws/resume-analysis/(?P<job_id>\w+)/$', consumers.ResumeAnalysisConsumer.as_asgi()),
]
```

---

## Step 5: Create Background Tasks

### 5.1 Create ecom/tasks.py
```python
import time
import logging
from pathlib import Path
from django.conf import settings
from django.utils import timezone
from channels.layers import get_channel_layer
from asgiref.sync import async_to_sync
from .models import ResumeAnalysisBatch, ResumeProcessingStatus, ApplyJob
from .ResumeParser.resume_analyzer import ResumeAnalyzer

logger = logging.getLogger(__name__)
channel_layer = get_channel_layer()

def start_batch_analysis(batch_id: str):
    """
    Start batch analysis processing
    This runs in the background and processes resumes in batches
    """
    try:
        batch = ResumeAnalysisBatch.objects.get(batch_id=batch_id)
        batch.status = 'PROCESSING'
        batch.started_at = timezone.now()
        batch.save()

        # Send initial status update
        send_batch_update(batch)

        # Get all pending resume statuses for this batch
        pending_statuses = ResumeProcessingStatus.objects.filter(
            batch=batch,
            status='PENDING'
        ).select_related('application')

        # Initialize analyzer
        analyzer = ResumeAnalyzer(batch.job.job_code)
        job_description = batch.job.job_description

        # Process resumes in batches
        batch_size = getattr(settings, 'RESUME_BATCH_SIZE', 2)

        for i in range(0, len(pending_statuses), batch_size):
            current_batch = pending_statuses[i:i + batch_size]
            process_resume_batch(current_batch, analyzer, job_description, batch)

            # Small delay between batches to prevent overwhelming the system
            time.sleep(1)

        # Mark batch as completed
        batch.status = 'COMPLETED'
        batch.completed_at = timezone.now()
        batch.save()

        # Send completion notification
        send_analysis_completed(batch)

    except ResumeAnalysisBatch.DoesNotExist:
        logger.error(f"Batch {batch_id} not found")
    except Exception as e:
        logger.error(f"Error in batch analysis {batch_id}: {str(e)}")
        try:
            batch = ResumeAnalysisBatch.objects.get(batch_id=batch_id)
            batch.status = 'FAILED'
            batch.error_message = str(e)
            batch.completed_at = timezone.now()
            batch.save()
            send_analysis_error(batch, str(e))
        except:
            pass

def process_resume_batch(resume_statuses, analyzer, job_description, batch):
    """Process a batch of resumes"""
    for status in resume_statuses:
        try:
            # Update status to processing
            status.status = 'PROCESSING'
            status.started_at = timezone.now()
            status.save()

            # Get resume file path
            resume_file_path = status.application.resume.path

            # Analyze the resume
            start_time = time.time()
            result = analyzer.analyze_single_resume(resume_file_path, job_description)
            processing_time = time.time() - start_time

            if result.get('success', False):
                # Save analysis results (your existing database save logic)
                web_json_data = analyzer.structure_web_json(result)
                from .ResumeParser.db_operations import create_or_update_json_analysis_db
                from .ResumeParser.resume_analysis_service import ResumeAnalysisService
                import json

                resume_attribute = create_or_update_json_analysis_db(
                    result['filename'],
                    batch.job.job_code,
                    web_json_data,
                    True
                )

                if resume_attribute:
                    ResumeAnalysisService.save_analysis_to_database(
                        json.loads(resume_attribute.analysis_json),
                        batch.job.job_code
                    )

                # Update status to completed
                status.status = 'COMPLETED'
                status.completed_at = timezone.now()
                status.processing_time = processing_time
                status.save()

                # Update batch counters
                batch.processed_resumes += 1
                batch.successful_resumes += 1
                batch.save()

                # Send individual resume completion update
                send_resume_completed(batch, status, result)

            else:
                # Handle failed analysis
                status.status = 'FAILED'
                status.error_message = result.get('error', 'Unknown error')
                status.completed_at = timezone.now()
                status.processing_time = processing_time
                status.save()

                # Update batch counters
                batch.processed_resumes += 1
                batch.failed_resumes += 1
                batch.save()

                # Send failure update
                send_resume_failed(batch, status, result.get('error', 'Unknown error'))

            # Send batch progress update
            send_batch_update(batch)

        except Exception as e:
            logger.error(f"Error processing resume {status.application.name}: {str(e)}")

            # Update status to failed
            status.status = 'FAILED'
            status.error_message = str(e)
            status.completed_at = timezone.now()
            status.save()

            # Update batch counters
            batch.processed_resumes += 1
            batch.failed_resumes += 1
            batch.save()

            # Send error update
            send_resume_failed(batch, status, str(e))
            send_batch_update(batch)

def send_batch_update(batch):
    """Send batch progress update via WebSocket"""
    room_group_name = f'resume_analysis_{batch.job.id}'

    async_to_sync(channel_layer.group_send)(
        room_group_name,
        {
            'type': 'batch_update',
            'data': {
                'type': 'batch_progress',
                'batch_id': str(batch.batch_id),
                'status': batch.status,
                'total_resumes': batch.total_resumes,
                'processed_resumes': batch.processed_resumes,
                'successful_resumes': batch.successful_resumes,
                'failed_resumes': batch.failed_resumes,
                'progress_percentage': batch.progress_percentage(),
                'estimated_time_remaining': batch.estimated_time_remaining()
            }
        }
    )

def send_resume_completed(batch, status, result):
    """Send individual resume completion update"""
    room_group_name = f'resume_analysis_{batch.job.id}'

    async_to_sync(channel_layer.group_send)(
        room_group_name,
        {
            'type': 'resume_completed',
            'data': {
                'type': 'resume_completed',
                'batch_id': str(batch.batch_id),
                'candidate_name': status.application.name,
                'final_score': result.get('final_score', 0),
                'recommendation': result.get('recommendation', {}).get('decision', 'UNKNOWN'),
                'processing_time': status.processing_time
            }
        }
    )

def send_resume_failed(batch, status, error_message):
    """Send individual resume failure update"""
    room_group_name = f'resume_analysis_{batch.job.id}'

    async_to_sync(channel_layer.group_send)(
        room_group_name,
        {
            'type': 'resume_completed',
            'data': {
                'type': 'resume_failed',
                'batch_id': str(batch.batch_id),
                'candidate_name': status.application.name,
                'error_message': error_message
            }
        }
    )

def send_analysis_completed(batch):
    """Send analysis completion notification"""
    room_group_name = f'resume_analysis_{batch.job.id}'

    async_to_sync(channel_layer.group_send)(
        room_group_name,
        {
            'type': 'analysis_completed',
            'data': {
                'type': 'analysis_completed',
                'batch_id': str(batch.batch_id),
                'total_resumes': batch.total_resumes,
                'successful_resumes': batch.successful_resumes,
                'failed_resumes': batch.failed_resumes,
                'processing_time': (batch.completed_at - batch.started_at).total_seconds() if batch.completed_at and batch.started_at else 0
            }
        }
    )

def send_analysis_error(batch, error_message):
    """Send analysis error notification"""
    room_group_name = f'resume_analysis_{batch.job.id}'

    async_to_sync(channel_layer.group_send)(
        room_group_name,
        {
            'type': 'analysis_error',
            'data': {
                'type': 'analysis_error',
                'batch_id': str(batch.batch_id),
                'error_message': error_message
            }
        }
    )
```

---

## Step 6: Update Django Views

### 6.1 Modify ecom/views.py - Update analyse_resumes function
```python
@login_required(login_url='adminlogin')
@csrf_exempt
def analyse_resumes(request):
    """
    Updated view to support WebSocket-based analysis
    GET: Render the analysis page
    POST: Legacy support (optional - can be removed if not needed)
    """
    if request.method == "GET":
        # Get job_id from URL parameter for WebSocket connection
        job_id = request.GET.get('job_id')
        if job_id:
            try:
                job = models.CreateJob.objects.get(id=job_id)
                mydict = {
                    "job_id": job_id,
                    "job_code": job.job_code,
                    "job_title": job.job_title,
                    "websocket_enabled": True
                }
                return render(request, "resumeparser/view-analysis.html", context=mydict)
            except models.CreateJob.DoesNotExist:
                mydict = {"error": "Job not found"}
                return render(request, "resumeparser/view-analysis.html", context=mydict)

        # Default behavior if no job_id provided
        mydict = {"websocket_enabled": False}
        return render(request, "resumeparser/view-analysis.html", context=mydict)

    elif request.method == "POST":
        # Legacy synchronous processing (optional - for backward compatibility)
        responseData = dict()
        data = json.loads(request.body)
        job = models.CreateJob.objects.get(id=data["job_id"])

        responseData["job_code"] = job.job_code

        job_description = job.job_description
        results = process_resumes(job_description, job.job_code)

        responseData["results"] = results
        responseData["analyseSuccess"] = True

        return JsonResponse(responseData)

# Add new API endpoint for batch status (optional - for REST API access)
@login_required(login_url='adminlogin')
def get_batch_status(request, batch_id):
    """Get current status of a batch analysis"""
    try:
        batch = models.ResumeAnalysisBatch.objects.get(batch_id=batch_id)

        # Get individual resume statuses
        resume_statuses = []
        for status in batch.resume_statuses.all():
            resume_statuses.append({
                'candidate_name': status.application.name,
                'status': status.status,
                'processing_time': status.processing_time,
                'error_message': status.error_message
            })

        return JsonResponse({
            'batch_id': str(batch.batch_id),
            'status': batch.status,
            'total_resumes': batch.total_resumes,
            'processed_resumes': batch.processed_resumes,
            'successful_resumes': batch.successful_resumes,
            'failed_resumes': batch.failed_resumes,
            'progress_percentage': batch.progress_percentage(),
            'estimated_time_remaining': batch.estimated_time_remaining(),
            'resume_statuses': resume_statuses,
            'created_at': batch.created_at.isoformat(),
            'started_at': batch.started_at.isoformat() if batch.started_at else None,
            'completed_at': batch.completed_at.isoformat() if batch.completed_at else None
        })

    except models.ResumeAnalysisBatch.DoesNotExist:
        return JsonResponse({'error': 'Batch not found'}, status=404)
```

### 6.2 Update ecommerce/urls.py
```python
# Add to your existing urlpatterns
from django.urls import path, include

urlpatterns = [
    # ... your existing patterns ...
    path("batch-status/<str:batch_id>/", views.get_batch_status, name="batch-status"),
]
```

---

## Step 7: Frontend Implementation

### 7.1 Update templates/resumeparser/view-analysis.html
```html
{% extends 'ecom/base.html' %}
{% load static %}

{% block title %}Resume Analysis{% endblock %}

{% block scripts %}
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js"></script>
<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>

<script>
$(document).ready(function() {
    let websocket = null;
    let currentBatchId = null;
    let reconnectAttempts = 0;
    const maxReconnectAttempts = 5;

    // WebSocket connection setup
    function connectWebSocket() {
        if (!{{ websocket_enabled|yesno:"true,false" }}) {
            console.log('WebSocket not enabled for this page');
            return;
        }

        const jobId = {{ job_id|default:"null" }};
        if (!jobId) {
            console.error('No job ID provided for WebSocket connection');
            return;
        }

        const wsScheme = window.location.protocol === "https:" ? "wss" : "ws";
        const wsPath = `${wsScheme}://${window.location.host}/ws/resume-analysis/${jobId}/`;

        websocket = new WebSocket(wsPath);

        websocket.onopen = function(e) {
            console.log('WebSocket connected');
            reconnectAttempts = 0;
            updateConnectionStatus(true);
        };

        websocket.onmessage = function(e) {
            const data = JSON.parse(e.data);
            handleWebSocketMessage(data);
        };

        websocket.onclose = function(e) {
            console.log('WebSocket closed');
            updateConnectionStatus(false);

            // Attempt to reconnect
            if (reconnectAttempts < maxReconnectAttempts) {
                reconnectAttempts++;
                console.log(`Attempting to reconnect (${reconnectAttempts}/${maxReconnectAttempts})`);
                setTimeout(connectWebSocket, 2000 * reconnectAttempts);
            }
        };

        websocket.onerror = function(e) {
            console.error('WebSocket error:', e);
            updateConnectionStatus(false);
        };
    }

    // Handle WebSocket messages
    function handleWebSocketMessage(data) {
        console.log('Received:', data);

        switch(data.type) {
            case 'analysis_started':
                handleAnalysisStarted(data);
                break;
            case 'batch_progress':
                handleBatchProgress(data);
                break;
            case 'resume_completed':
                handleResumeCompleted(data);
                break;
            case 'resume_failed':
                handleResumeFailed(data);
                break;
            case 'analysis_completed':
                handleAnalysisCompleted(data);
                break;
            case 'analysis_error':
                handleAnalysisError(data);
                break;
            case 'error':
                handleError(data);
                break;
        }
    }

    // Event handlers
    function handleAnalysisStarted(data) {
        currentBatchId = data.batch_id;
        showProgressModal();
        updateProgress(0, data.total_resumes, 0, 0);
        addLogMessage(`Analysis started for ${data.total_resumes} resumes`);
    }

    function handleBatchProgress(data) {
        updateProgress(
            data.progress_percentage,
            data.total_resumes,
            data.processed_resumes,
            data.successful_resumes,
            data.failed_resumes,
            data.estimated_time_remaining
        );
    }

    function handleResumeCompleted(data) {
        if (data.type === 'resume_completed') {
            addLogMessage(`✅ ${data.candidate_name} - Score: ${data.final_score} - ${data.recommendation}`);
        } else if (data.type === 'resume_failed') {
            addLogMessage(`❌ ${data.candidate_name} - Failed: ${data.error_message}`);
        }
    }

    function handleResumeFailed(data) {
        addLogMessage(`❌ ${data.candidate_name} - Failed: ${data.error_message}`);
    }

    function handleAnalysisCompleted(data) {
        addLogMessage(`🎉 Analysis completed! ${data.successful_resumes}/${data.total_resumes} successful`);

        setTimeout(() => {
            hideProgressModal();
            // Refresh page to show results
            window.location.reload();
        }, 2000);
    }

    function handleAnalysisError(data) {
        addLogMessage(`💥 Analysis failed: ${data.error_message}`);
        setTimeout(() => {
            hideProgressModal();
        }, 3000);
    }

    function handleError(data) {
        addLogMessage(`⚠️ Error: ${data.message}`);
    }

    // UI update functions
    function showProgressModal() {
        const modal = new bootstrap.Modal(document.getElementById('progressModal'));
        modal.show();
    }

    function hideProgressModal() {
        const modal = bootstrap.Modal.getInstance(document.getElementById('progressModal'));
        if (modal) {
            modal.hide();
        }
    }

    function updateProgress(percentage, total, processed, successful, failed, estimatedTime) {
        // Update progress bar
        const progressBar = document.getElementById('progressBar');
        progressBar.style.width = `${percentage}%`;
        progressBar.setAttribute('aria-valuenow', percentage);
        progressBar.textContent = `${percentage}%`;

        // Update counters
        document.getElementById('totalResumes').textContent = total;
        document.getElementById('processedResumes').textContent = processed;
        document.getElementById('successfulResumes').textContent = successful || 0;
        document.getElementById('failedResumes').textContent = failed || 0;

        // Update estimated time
        if (estimatedTime) {
            const minutes = Math.floor(estimatedTime / 60);
            const seconds = Math.floor(estimatedTime % 60);
            document.getElementById('estimatedTime').textContent = `${minutes}m ${seconds}s`;
        } else {
            document.getElementById('estimatedTime').textContent = 'Calculating...';
        }
    }

    function addLogMessage(message) {
        const logContainer = document.getElementById('analysisLog');
        const timestamp = new Date().toLocaleTimeString();
        const logEntry = document.createElement('div');
        logEntry.className = 'log-entry';
        logEntry.innerHTML = `<small class="text-muted">${timestamp}</small> ${message}`;
        logContainer.appendChild(logEntry);
        logContainer.scrollTop = logContainer.scrollHeight;
    }

    function updateConnectionStatus(connected) {
        const statusElement = document.getElementById('connectionStatus');
        if (connected) {
            statusElement.innerHTML = '<span class="text-success">🟢 Connected</span>';
        } else {
            statusElement.innerHTML = '<span class="text-danger">🔴 Disconnected</span>';
        }
    }

    // Start analysis button click
    $('#startAnalysisBtn').click(function() {
        if (websocket && websocket.readyState === WebSocket.OPEN) {
            websocket.send(JSON.stringify({
                action: 'start_analysis'
            }));
        } else {
            alert('WebSocket connection not available. Please refresh the page.');
        }
    });

    // Initialize WebSocket connection
    connectWebSocket();

    // Legacy support for non-WebSocket analysis
    {% if not websocket_enabled %}
    $('#startAnalysisBtn').click(function() {
        // Your existing AJAX code here for backward compatibility
        const modal = new bootstrap.Modal(document.getElementById('legacyModal'));
        modal.show();

        $.ajax({
            type: "POST",
            url: "{% url 'analyse_resumes' %}",
            data: JSON.stringify({ job_id: {{ job_id|default:"null" }} }),
            contentType: "application/json",
            success: function (data) {
                if(data.analyseSuccess) {
                    setTimeout(() => {
                        modal.hide();
                        window.location.reload();
                    }, 1000);
                }
            },
            error: function (err) {
                console.error("Error:", err);
                modal.hide();
            }
        });
    });
    {% endif %}
});
</script>
{% endblock scripts %}

{% block content %}
<br><br><br>

<div class="container mt-4" id="container">
    <h2 class="mb-4 fw-bold text-primary d-flex justify-content-between">
        📂 Resume Analysis
        {% if websocket_enabled %}
            <div>
                <span id="connectionStatus" class="me-3"></span>
                <button class="btn btn-primary btn-sm" id="startAnalysisBtn">New Analysis</button>
            </div>
        {% else %}
            <button class="btn btn-primary btn-sm" id="startAnalysisBtn">New Analysis</button>
        {% endif %}
    </h2>

    {% if error %}
        <div class="alert alert-danger">{{ error }}</div>
    {% endif %}

    {% if job_code %}
        <div class="alert alert-info">
            <strong>Job:</strong> {{ job_title }} ({{ job_code }})
        </div>
    {% endif %}

    {% if output_file_names %}
        <div class="row row-cols-1 row-cols-md-2 row-cols-lg-3 g-4">
            {% for filename in output_file_names %}
                <div class="col">
                    <div class="card shadow-sm h-100 border-0 rounded-3">
                        <div class="card-body bg-light d-flex flex-column justify-content-center align-items-start">
                            <h5 class="card-title fw-semibold text-dark mb-2">
                                <i class="bi bi-file-earmark-text-fill me-2"></i> {{ filename }}
                            </h5>
                            <a href="{{request.path}}/{{filename}}" class="btn btn-outline-primary btn-sm mt-auto">View Details</a>
                        </div>
                    </div>
                </div>
            {% endfor %}
        </div>
    {% else %}
        <div class="alert alert-info">No analysis results found. Click "New Analysis" to start processing resumes.</div>
    {% endif %}
</div>

<!-- WebSocket Progress Modal -->
{% if websocket_enabled %}
<div class="modal fade" id="progressModal" tabindex="-1" aria-labelledby="progressModalLabel" aria-hidden="true" data-bs-backdrop="static" data-bs-keyboard="false">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="progressModalLabel">🔄 Resume Analysis in Progress</h5>
            </div>
            <div class="modal-body">
                <!-- Progress Bar -->
                <div class="mb-4">
                    <div class="d-flex justify-content-between mb-2">
                        <span>Overall Progress</span>
                        <span id="estimatedTime">Calculating...</span>
                    </div>
                    <div class="progress" style="height: 25px;">
                        <div class="progress-bar progress-bar-striped progress-bar-animated"
                             role="progressbar"
                             id="progressBar"
                             style="width: 0%"
                             aria-valuenow="0"
                             aria-valuemin="0"
                             aria-valuemax="100">0%</div>
                    </div>
                </div>

                <!-- Statistics -->
                <div class="row mb-4">
                    <div class="col-3">
                        <div class="text-center">
                            <div class="h4 mb-0" id="totalResumes">0</div>
                            <small class="text-muted">Total</small>
                        </div>
                    </div>
                    <div class="col-3">
                        <div class="text-center">
                            <div class="h4 mb-0" id="processedResumes">0</div>
                            <small class="text-muted">Processed</small>
                        </div>
                    </div>
                    <div class="col-3">
                        <div class="text-center">
                            <div class="h4 mb-0 text-success" id="successfulResumes">0</div>
                            <small class="text-muted">Successful</small>
                        </div>
                    </div>
                    <div class="col-3">
                        <div class="text-center">
                            <div class="h4 mb-0 text-danger" id="failedResumes">0</div>
                            <small class="text-muted">Failed</small>
                        </div>
                    </div>
                </div>

                <!-- Live Log -->
                <div class="card">
                    <div class="card-header">
                        <h6 class="mb-0">📋 Processing Log</h6>
                    </div>
                    <div class="card-body p-2" style="height: 200px; overflow-y: auto; background-color: #f8f9fa;">
                        <div id="analysisLog" style="font-family: monospace; font-size: 0.9em;">
                            <!-- Log entries will be added here -->
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endif %}

<!-- Legacy Modal (for backward compatibility) -->
{% if not websocket_enabled %}
<div class="modal fade" id="legacyModal" tabindex="-1" aria-labelledby="legacyModalLabel" aria-hidden="true" data-bs-backdrop="static" data-bs-keyboard="false">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="legacyModalLabel">Processing Resumes</h5>
            </div>
            <div class="modal-body text-center">
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">Loading...</span>
                </div>
                <p class="mt-3">Analyzing resumes... Please wait.</p>
            </div>
        </div>
    </div>
</div>
{% endif %}

<br><br>
{% endblock content %}
```

---

## Step 8: Setup and Testing

### 8.1 Install and Start Redis Server
```bash
# Windows (using Chocolatey)
choco install redis-64

# Or download from: https://github.com/microsoftarchive/redis/releases
# Start Redis server
redis-server

# Test Redis connection
redis-cli ping
# Should return: PONG
```

### 8.2 Run Database Migrations
```bash
python manage.py makemigrations
python manage.py migrate
```

### 8.3 Start Django with Daphne (ASGI Server)

**Method 1: Using Django's runserver (Recommended for Development)**
```bash
# Since 'daphne' is in INSTALLED_APPS, runserver will automatically use Daphne
python manage.py runserver

# Or specify port
python manage.py runserver 8000

# This now supports both HTTP and WebSocket connections!
```

**Method 2: Direct Daphne Command (For Production)**
```bash
# Start Django with Daphne ASGI server directly
daphne -p 8000 ecommerce.asgi:application

# For development with auto-reload and verbose logging
daphne -p 8000 -v 2 --reload ecommerce.asgi:application

# For production (bind to all interfaces)
daphne -b 0.0.0.0 -p 8000 ecommerce.asgi:application
```

**Key Point**: With `'daphne'` in `INSTALLED_APPS`, you can use the familiar `python manage.py runserver` command and it will automatically run with Daphne's ASGI server, supporting WebSockets!

### 8.4 Test the Implementation

1. **Start Redis server** (must be running before Daphne)
   ```bash
   redis-server
   ```

2. **Start Django with Daphne** (in a separate terminal)
   ```bash
   # Since 'daphne' is in INSTALLED_APPS, you can use:
   python manage.py runserver

   # Or use Daphne directly:
   daphne -p 8000 -v 2 --reload ecommerce.asgi:application
   ```

3. **Upload resumes** to a job posting through your Django admin or forms

4. **Navigate** to the analysis page with `?job_id=X` parameter
   ```
   http://localhost:8000/analyse-pdfs/?job_id=1
   ```

5. **Click "New Analysis"** - should open WebSocket connection (check browser console for connection logs)

6. **Watch real-time updates** as resumes are processed in batches of 2

7. **Verify** that results appear without page refresh

### 8.5 Troubleshooting Daphne Setup

**If WebSocket connection fails:**
```bash
# Check if Daphne is running with WebSocket support
curl -i -N -H "Connection: Upgrade" -H "Upgrade: websocket" -H "Sec-WebSocket-Version: 13" -H "Sec-WebSocket-Key: test" http://localhost:8000/ws/resume-analysis/1/
```

**Common issues:**
- **Redis not running**: Start Redis server first
- **Port conflicts**: Use different port with `-p 8001`
- **ASGI routing errors**: Check `ecom/routing.py` and `ecommerce/asgi.py`
- **Authentication issues**: Ensure user is logged in before connecting WebSocket

---

## Step 9: Production Deployment with Daphne

### 9.1 Production Daphne Configuration

**Create a production startup script** (`start_daphne.sh`):
```bash
#!/bin/bash
# Production Daphne startup script

# Set environment variables
export DJANGO_SETTINGS_MODULE=ecommerce.settings
export PYTHONPATH=/path/to/your/project

# Start Daphne with production settings
daphne -b 0.0.0.0 -p 8000 \
       --access-log /var/log/daphne/access.log \
       --application-close-timeout 60 \
       --websocket_timeout 86400 \
       ecommerce.asgi:application
```

**Systemd service file** (`/etc/systemd/system/daphne-resume-analysis.service`):
```ini
[Unit]
Description=Daphne Resume Analysis WebSocket Server
After=network.target redis.service

[Service]
Type=simple
User=www-data
Group=www-data
WorkingDirectory=/path/to/your/project
Environment=DJANGO_SETTINGS_MODULE=ecommerce.settings
ExecStart=/path/to/venv/bin/daphne -b 0.0.0.0 -p 8000 ecommerce.asgi:application
Restart=always
RestartSec=3

[Install]
WantedBy=multi-user.target
```

### 9.2 Nginx Configuration for WebSocket Proxy
```nginx
upstream daphne_backend {
    server {{ LOCALHOST_IP }}:8000;
}

server {
    listen 80;
    server_name your-domain.com;

    # Static files
    location /static/ {
        alias /path/to/your/static/files/;
    }

    # WebSocket upgrade
    location /ws/ {
        proxy_pass http://daphne_backend;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_read_timeout 86400;
    }

    # Regular HTTP requests
    location / {
        proxy_pass http://daphne_backend;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
```

### 9.3 Background Task Processing with Daphne
For production, consider using Celery alongside Daphne:

```python
# In tasks.py, convert functions to Celery tasks
from celery import shared_task

@shared_task
def start_batch_analysis_task(batch_id: str):
    return start_batch_analysis(batch_id)

# In consumers.py, call the Celery task
await database_sync_to_async(start_batch_analysis_task.delay)(str(batch.batch_id))
```

**Run both Django (with Daphne) and Celery in production:**
```bash
# Terminal 1: Start Django with Daphne for WebSocket handling
python manage.py runserver 0.0.0.0:8000
# Or directly: daphne -b 0.0.0.0 -p 8000 ecommerce.asgi:application

# Terminal 2: Start Celery worker for background tasks
celery -A ecommerce worker --loglevel=info

# Terminal 3: Start Redis server
redis-server
```

### 9.4 Monitoring and Logging
- **Daphne logs**: Configure access and error logs
- **WebSocket monitoring**: Track connection counts and failures
- **Redis monitoring**: Monitor memory usage and connection counts
- **Performance metrics**: Track resume processing times and batch completion rates

### 9.5 Scaling Considerations
- **Multiple Daphne instances**: Run behind a load balancer
- **Redis Cluster**: For high availability and scaling
- **Horizontal scaling**: Multiple servers with shared Redis backend
- **Connection limits**: Configure appropriate WebSocket timeout and connection limits

---

## Step 10: Complete Development Workflow

### 10.1 Development Setup (Step-by-Step)

1. **Install dependencies**:
   ```bash
   pip install channels==4.0.0 channels-redis==4.1.0 redis==5.0.1 daphne==4.0.0
   ```

2. **Start Redis server** (Terminal 1):
   ```bash
   redis-server
   ```

3. **Run migrations**:
   ```bash
   python manage.py makemigrations
   python manage.py migrate
   ```

4. **Start Django server** (Terminal 2):
   ```bash
   # With 'daphne' in INSTALLED_APPS, runserver uses Daphne automatically
   python manage.py runserver

   # Or use Daphne directly for more control:
   daphne -p 8000 -v 2 --reload ecommerce.asgi:application
   ```

5. **Test WebSocket connection** in browser console:
   ```javascript
   const ws = new WebSocket('ws://localhost:8000/ws/resume-analysis/1/');
   ws.onopen = () => console.log('Connected');
   ws.onmessage = (e) => console.log('Message:', JSON.parse(e.data));
   ```

### 10.2 Usage Flow Summary

1. **User uploads 20 resumes** to job posting
2. **Navigate to analysis page**: `http://localhost:8000/analyse-pdfs/?job_id=1`
3. **User clicks "New Analysis"** → WebSocket connects via Daphne
4. **Backend creates batch** with 20 resume records
5. **Processing starts** → 2 resumes at a time in background
6. **Real-time updates via WebSocket** → Progress bar, individual completions
7. **User sees live results** → No page refresh needed
8. **Analysis completes** → Final summary and automatic page refresh

### 10.3 Key Advantages of Daphne Implementation

- **Seamless Integration**: Adding `'daphne'` to `INSTALLED_APPS` makes `runserver` use Daphne automatically
- **Native WebSocket Support**: Built specifically for Django Channels
- **HTTP + WebSocket**: Single server handles both protocols
- **Familiar Commands**: Use `python manage.py runserver` as usual
- **Development Friendly**: Auto-reload works with runserver
- **Production Ready**: Suitable for production deployment
- **Nginx Compatible**: Easy to proxy behind Nginx
- **Scalable**: Can run multiple instances behind load balancer

This implementation provides a smooth, real-time experience while processing resumes efficiently in the background using Daphne as the ASGI server.
```
```
