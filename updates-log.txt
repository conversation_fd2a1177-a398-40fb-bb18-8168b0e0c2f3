__________________________________________________________________________________________________________________________________________________________________________________________________________________________________________________________________________________________________________________
ID      Commit Message                                              -   Commit Code                                     -   Commit Description
__________________________________________________________________________________________________________________________________________________________________________________________________________________________________________________________________________________________________________________
1       first commit                                                -   f16ae8ee8543fedf0e2a3af11fc80d53ce5c4099        -   E-commerce Project
__________________________________________________________________________________________________________________________________________________________________________________________________________________________________________________________________________________________________________________
2       path changes                                                -   be137467c089cbb6f8c38e42803c1955b594f071        -   Fixed Project Path issues.
__________________________________________________________________________________________________________________________________________________________________________________________________________________________________________________________________________________________________________________
3       Resume Parser Integration                                   -   717f05c6baefbe8046e71f7248980e559f5df3cd        -   Integrated Resumer Parser Code
__________________________________________________________________________________________________________________________________________________________________________________________________________________________________________________________________________________________________________________
4       Resume Parser v0.0.2                                        -   7ac8a615f06cf0a5ec1c253d61def2c155643730        -   Resume Parser v0.0.2
__________________________________________________________________________________________________________________________________________________________________________________________________________________________________________________________________________________________________________________
5       created_resume_attributes_and_resume_logs_table             -   83e6e4ed5530c6cd56d398717374b692d4c4bf53        -   created_resume_attributes_and_resume_logs_table
__________________________________________________________________________________________________________________________________________________________________________________________________________________________________________________________________________________________________________________
6       bug fixes - analysis values are missing etc.                -   dc15abb5152af134da81d9e70a097d58f50c1085        -   Fixed some bugs: analysis values are missing etc.
__________________________________________________________________________________________________________________________________________________________________________________________________________________________________________________________________________________________________________________
7       migrated from json_files to database                        -   acd6042eac812e65d026ef56c39e4a601ddf1468        -   Migrated analysis saving and fetching to database.
__________________________________________________________________________________________________________________________________________________________________________________________________________________________________________________________________________________________________________________
8       changed llm model, add custom attributes functionality      -   c88886a8d2a7afdd8fd50465568c5dc66bbb7808        -   Changed LLM model to "llama3.1:8b-instruct-q4_K_M" and Implemented Custom Attributes functionality.
__________________________________________________________________________________________________________________________________________________________________________________________________________________________________________________________________________________________________________________
9       migrated everything to database                             -   1e017073bd2b24bb03c95b646cc6a7ee7df27ebd        -   migrated everything to database
__________________________________________________________________________________________________________________________________________________________________________________________________________________________________________________________________________________________________________________
10      Bug fixes - Average Job Change etc.                         -   ab45ee96cb9f10f58383b3f9213a556d1610cff3        -   Fixed Average Job Change issue , job description display format issues, summary content not displaying 
__________________________________________________________________________________________________________________________________________________________________________________________________________________________________________________________________________________________________________________
11      Tested Websocket Implementation                             -                                                   -   Tested Websocket Implementation 