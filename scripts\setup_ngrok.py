#!/usr/bin/env python3
"""
Ngrok Setup Script for Django E-commerce Project
Automatically configures and starts ngrok tunnel for internet exposure
"""

import os
import sys
import json
import subprocess
import time
import requests
from pathlib import Path

# Add project root to Python path
project_root = Path(__file__).parent.parent
sys.path.append(str(project_root))

class NgrokManager:
    def __init__(self):
        self.ngrok_api_url = "http://localhost:4040/api/tunnels"
        self.django_port = 8000
        self.project_root = project_root
        
    def check_ngrok_installed(self):
        """Check if ngrok is installed"""
        try:
            result = subprocess.run(['ngrok', 'version'], 
                                  capture_output=True, text=True, check=True)
            print(f"✅ Ngrok is installed: {result.stdout.strip()}")
            return True
        except (subprocess.CalledProcessError, FileNotFoundError):
            print("❌ Ngrok is not installed or not in PATH")
            return False
    
    def install_ngrok_instructions(self):
        """Provide installation instructions for ngrok"""
        print("\n📋 To install ngrok:")
        print("1. Go to https://ngrok.com/download")
        print("2. Download ngrok for your operating system")
        print("3. Extract and add to your PATH")
        print("4. Sign up for a free account at https://ngrok.com/")
        print("5. Get your auth token from https://dashboard.ngrok.com/get-started/your-authtoken")
        print("6. Run: ngrok config add-authtoken YOUR_AUTH_TOKEN")
        print("\nAlternatively, install via package manager:")
        print("- Windows (Chocolatey): choco install ngrok")
        print("- macOS (Homebrew): brew install ngrok/ngrok/ngrok")
        print("- Linux (Snap): snap install ngrok")
    
    def check_ngrok_auth(self):
        """Check if ngrok is authenticated"""
        try:
            # Try to start a test tunnel briefly
            process = subprocess.Popen(['ngrok', 'http', '8080', '--log=stdout'], 
                                     stdout=subprocess.PIPE, stderr=subprocess.PIPE)
            time.sleep(2)
            process.terminate()
            
            # Check if authentication error occurred
            stdout, stderr = process.communicate()
            if b"authentication failed" in stderr or b"authentication failed" in stdout:
                print("❌ Ngrok authentication failed")
                print("Please run: ngrok config add-authtoken YOUR_AUTH_TOKEN")
                return False
            
            print("✅ Ngrok is authenticated")
            return True
        except Exception as e:
            print(f"⚠️  Could not verify ngrok authentication: {e}")
            return True  # Assume it's okay and let ngrok handle it
    
    def get_active_tunnels(self):
        """Get currently active ngrok tunnels"""
        try:
            response = requests.get(self.ngrok_api_url, timeout=5)
            if response.status_code == 200:
                return response.json().get('tunnels', [])
        except requests.exceptions.RequestException:
            pass
        return []
    
    def find_django_tunnel(self):
        """Find existing Django tunnel"""
        tunnels = self.get_active_tunnels()
        for tunnel in tunnels:
            config = tunnel.get('config', {})
            if config.get('addr') == f'http://localhost:{self.django_port}':
                return tunnel
        return None
    
    def start_ngrok_tunnel(self):
        """Start ngrok tunnel for Django"""
        print(f"🚀 Starting ngrok tunnel for Django on port {self.django_port}...")
        
        # Start ngrok in background
        cmd = ['ngrok', 'http', str(self.django_port), '--log=stdout']
        process = subprocess.Popen(cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE)
        
        # Wait for ngrok to start
        print("⏳ Waiting for ngrok to initialize...")
        time.sleep(3)
        
        # Check if process is still running
        if process.poll() is not None:
            stdout, stderr = process.communicate()
            print(f"❌ Ngrok failed to start:")
            print(f"STDOUT: {stdout.decode()}")
            print(f"STDERR: {stderr.decode()}")
            return None, None
        
        # Get tunnel information
        tunnel = self.find_django_tunnel()
        if tunnel:
            public_url = tunnel['public_url']
            domain = public_url.replace('https://', '').replace('http://', '')
            print(f"✅ Ngrok tunnel started successfully!")
            print(f"🌐 Public URL: {public_url}")
            print(f"🔗 Domain: {domain}")
            return public_url, domain
        else:
            print("❌ Could not retrieve tunnel information")
            return None, None
    
    def update_django_settings(self, domain):
        """Update Django settings with ngrok domain"""
        if not domain:
            return False
        
        # Set environment variable
        os.environ['NGROK_DOMAIN'] = domain
        os.environ['NGROK_ENABLED'] = 'true'
        
        print(f"✅ Environment variables set:")
        print(f"   NGROK_DOMAIN={domain}")
        print(f"   NGROK_ENABLED=true")
        
        return True
    
    def create_env_file(self, domain):
        """Create .env file with ngrok configuration"""
        env_file = self.project_root / '.env'
        
        env_content = f"""# Ngrok Configuration
NGROK_ENABLED=true
NGROK_DOMAIN={domain}

# Django Settings
DEBUG=True
"""
        
        with open(env_file, 'w') as f:
            f.write(env_content)
        
        print(f"✅ Created .env file with ngrok configuration")
    
    def show_usage_instructions(self, public_url, domain):
        """Show usage instructions"""
        print("\n" + "="*60)
        print("🎉 NGROK TUNNEL IS READY!")
        print("="*60)
        print(f"🌐 Your Django app is now accessible at:")
        print(f"   {public_url}")
        print(f"\n📱 Test URLs:")
        print(f"   Homepage: {public_url}/")
        print(f"   Admin: {public_url}/admin/")
        print(f"   API: {public_url}/api/")
        print(f"\n🔧 Configuration:")
        print(f"   Domain added to ALLOWED_HOSTS: {domain}")
        print(f"   Environment variables set automatically")
        print(f"\n⚠️  Important Notes:")
        print(f"   - Keep this terminal open to maintain the tunnel")
        print(f"   - Free ngrok URLs change on restart")
        print(f"   - For permanent URLs, upgrade to ngrok Pro")
        print(f"   - Test webhooks and external integrations now work!")
        print("\n🛑 To stop: Press Ctrl+C")
        print("="*60)

def main():
    print("🔧 Django Ngrok Setup Script")
    print("="*40)
    
    manager = NgrokManager()
    
    # Check if ngrok is installed
    if not manager.check_ngrok_installed():
        manager.install_ngrok_instructions()
        return 1
    
    # Check authentication
    if not manager.check_ngrok_auth():
        return 1
    
    # Check for existing tunnel
    existing_tunnel = manager.find_django_tunnel()
    if existing_tunnel:
        public_url = existing_tunnel['public_url']
        domain = public_url.replace('https://', '').replace('http://', '')
        print(f"✅ Found existing tunnel: {public_url}")
        manager.update_django_settings(domain)
        manager.create_env_file(domain)
        manager.show_usage_instructions(public_url, domain)
        return 0
    
    # Start new tunnel
    public_url, domain = manager.start_ngrok_tunnel()
    if not public_url:
        return 1
    
    # Update Django configuration
    manager.update_django_settings(domain)
    manager.create_env_file(domain)
    
    # Show instructions
    manager.show_usage_instructions(public_url, domain)
    
    try:
        # Keep script running
        print("\n⏳ Keeping tunnel alive... (Press Ctrl+C to stop)")
        while True:
            time.sleep(10)
            # Check if tunnel is still active
            if not manager.find_django_tunnel():
                print("❌ Tunnel disconnected!")
                break
    except KeyboardInterrupt:
        print("\n🛑 Stopping ngrok tunnel...")
        return 0

if __name__ == "__main__":
    sys.exit(main())
