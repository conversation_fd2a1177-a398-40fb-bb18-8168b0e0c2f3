#!/usr/bin/env python3
"""
One-click Django + Ngrok Startup Script
Starts both Django development server and ngrok tunnel simultaneously
"""

import os
import sys
import time
import subprocess
import threading
import signal
from pathlib import Path

class DjangoNgrokLauncher:
    def __init__(self):
        self.django_process = None
        self.ngrok_process = None
        self.django_port = 8000
        self.project_root = Path(__file__).parent
        
    def check_dependencies(self):
        """Check if required tools are available"""
        missing = []
        
        # Check Python
        try:
            subprocess.run([sys.executable, '--version'], 
                         capture_output=True, check=True)
        except:
            missing.append('Python')
        
        # Check Django
        try:
            subprocess.run([sys.executable, '-c', 'import django'], 
                         capture_output=True, check=True)
        except:
            missing.append('Django')
        
        # Check ngrok
        try:
            subprocess.run(['ngrok', 'version'], 
                         capture_output=True, check=True)
        except:
            missing.append('ngrok')
        
        if missing:
            print(f"❌ Missing dependencies: {', '.join(missing)}")
            print("\nInstallation instructions:")
            if 'ngrok' in missing:
                print("- Ngrok: https://ngrok.com/download")
            if 'Django' in missing:
                print("- Django: pip install django")
            return False
        
        print("✅ All dependencies available")
        return True
    
    def start_django(self):
        """Start Django development server"""
        print(f"🚀 Starting Django server on port {self.django_port}...")
        
        # Change to project directory
        os.chdir(self.project_root)
        
        # Start Django server
        cmd = [sys.executable, 'manage.py', 'runserver', f'0.0.0.0:{self.django_port}']
        self.django_process = subprocess.Popen(
            cmd, 
            stdout=subprocess.PIPE, 
            stderr=subprocess.STDOUT,
            universal_newlines=True,
            bufsize=1
        )
        
        # Monitor Django startup
        django_ready = False
        for line in iter(self.django_process.stdout.readline, ''):
            print(f"Django: {line.strip()}")
            if "Starting development server" in line or "Quit the server" in line:
                django_ready = True
                break
            if self.django_process.poll() is not None:
                print("❌ Django failed to start")
                return False
        
        if django_ready:
            print("✅ Django server is running")
            return True
        
        return False
    
    def start_ngrok(self):
        """Start ngrok tunnel"""
        print(f"🌐 Starting ngrok tunnel for port {self.django_port}...")
        
        # Wait a moment for Django to fully start
        time.sleep(2)
        
        # Start ngrok
        cmd = ['ngrok', 'http', str(self.django_port)]
        self.ngrok_process = subprocess.Popen(
            cmd,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE
        )
        
        # Wait for ngrok to start
        time.sleep(3)
        
        # Get tunnel info
        try:
            import requests
            response = requests.get('http://localhost:4040/api/tunnels', timeout=5)
            if response.status_code == 200:
                tunnels = response.json().get('tunnels', [])
                for tunnel in tunnels:
                    if tunnel.get('config', {}).get('addr') == f'http://localhost:{self.django_port}':
                        public_url = tunnel['public_url']
                        domain = public_url.replace('https://', '').replace('http://', '')
                        
                        # Set environment variables
                        os.environ['NGROK_DOMAIN'] = domain
                        os.environ['NGROK_ENABLED'] = 'true'
                        
                        print("✅ Ngrok tunnel is ready!")
                        print(f"🌐 Public URL: {public_url}")
                        print(f"🔗 Domain: {domain}")
                        
                        self.show_success_info(public_url)
                        return True
        except Exception as e:
            print(f"⚠️  Could not get tunnel info: {e}")
        
        print("❌ Failed to start ngrok tunnel")
        return False
    
    def show_success_info(self, public_url):
        """Show success information"""
        print("\n" + "="*60)
        print("🎉 DJANGO + NGROK ARE READY!")
        print("="*60)
        print(f"🌐 Your app is live at: {public_url}")
        print(f"📱 Test URLs:")
        print(f"   Homepage: {public_url}/")
        print(f"   Admin: {public_url}/admin/")
        print(f"   Resume Parser: {public_url}/analyse-pdfs/")
        print(f"   Face Login: {public_url}/face-login/")
        print(f"\n🔧 Local URLs:")
        print(f"   Django: http://localhost:{self.django_port}/")
        print(f"   Ngrok Dashboard: http://localhost:4040/")
        print(f"\n⚠️  Important:")
        print(f"   - Keep this terminal open")
        print(f"   - Free ngrok URLs change on restart")
        print(f"   - Perfect for testing webhooks & APIs")
        print(f"\n🛑 To stop: Press Ctrl+C")
        print("="*60)
    
    def cleanup(self):
        """Clean up processes"""
        print("\n🛑 Shutting down...")
        
        if self.ngrok_process:
            print("Stopping ngrok...")
            self.ngrok_process.terminate()
            try:
                self.ngrok_process.wait(timeout=5)
            except subprocess.TimeoutExpired:
                self.ngrok_process.kill()
        
        if self.django_process:
            print("Stopping Django...")
            self.django_process.terminate()
            try:
                self.django_process.wait(timeout=5)
            except subprocess.TimeoutExpired:
                self.django_process.kill()
        
        print("✅ Cleanup complete")
    
    def run(self):
        """Main execution"""
        print("🔧 Django + Ngrok Launcher")
        print("="*40)
        
        # Check dependencies
        if not self.check_dependencies():
            return 1
        
        try:
            # Start Django in a separate thread
            django_thread = threading.Thread(target=self.start_django)
            django_thread.daemon = True
            django_thread.start()
            
            # Wait for Django to start
            time.sleep(5)
            
            # Start ngrok
            if not self.start_ngrok():
                return 1
            
            # Keep running
            print("\n⏳ Services are running... (Press Ctrl+C to stop)")
            
            # Handle Django output in background
            if self.django_process:
                def print_django_output():
                    for line in iter(self.django_process.stdout.readline, ''):
                        if line.strip():
                            print(f"Django: {line.strip()}")
                
                output_thread = threading.Thread(target=print_django_output)
                output_thread.daemon = True
                output_thread.start()
            
            # Wait for interrupt
            while True:
                time.sleep(1)
                
                # Check if processes are still running
                if self.django_process and self.django_process.poll() is not None:
                    print("❌ Django process stopped unexpectedly")
                    break
                
                if self.ngrok_process and self.ngrok_process.poll() is not None:
                    print("❌ Ngrok process stopped unexpectedly")
                    break
        
        except KeyboardInterrupt:
            pass
        except Exception as e:
            print(f"❌ Error: {e}")
            return 1
        finally:
            self.cleanup()
        
        return 0

def signal_handler(sig, frame):
    """Handle Ctrl+C gracefully"""
    print("\n🛑 Received interrupt signal...")
    sys.exit(0)

def main():
    # Set up signal handler
    signal.signal(signal.SIGINT, signal_handler)
    
    # Run launcher
    launcher = DjangoNgrokLauncher()
    return launcher.run()

if __name__ == "__main__":
    sys.exit(main())
