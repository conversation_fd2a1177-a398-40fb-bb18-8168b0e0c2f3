"""
Django management command to start ngrok tunnel
Usage: python manage.py start_ngrok
"""

import os
import sys
import json
import time
import subprocess
import requests
from django.core.management.base import BaseCommand, CommandError
from django.conf import settings


class Command(BaseCommand):
    help = 'Start ngrok tunnel for Django development server'
    
    def add_arguments(self, parser):
        parser.add_argument(
            '--port',
            type=int,
            default=8000,
            help='Django server port (default: 8000)'
        )
        parser.add_argument(
            '--subdomain',
            type=str,
            help='Custom subdomain (requires ngrok Pro)'
        )
        parser.add_argument(
            '--region',
            type=str,
            choices=['us', 'eu', 'ap', 'au', 'sa', 'jp', 'in'],
            default='us',
            help='Ngrok region (default: us)'
        )
        parser.add_argument(
            '--auth-token',
            type=str,
            help='Ngrok auth token (if not configured)'
        )
    
    def handle(self, *args, **options):
        self.port = options['port']
        self.subdomain = options['subdomain']
        self.region = options['region']
        self.auth_token = options['auth_token']
        
        self.stdout.write(
            self.style.SUCCESS('🔧 Starting ngrok tunnel for Django...')
        )
        
        # Check if ngrok is installed
        if not self.check_ngrok_installed():
            raise CommandError('Ngrok is not installed. Please install it first.')
        
        # Configure auth token if provided
        if self.auth_token:
            self.configure_auth_token()
        
        # Check for existing tunnel
        existing_tunnel = self.find_existing_tunnel()
        if existing_tunnel:
            self.handle_existing_tunnel(existing_tunnel)
            return
        
        # Start new tunnel
        tunnel_info = self.start_tunnel()
        if tunnel_info:
            self.update_django_config(tunnel_info)
            self.show_success_message(tunnel_info)
        else:
            raise CommandError('Failed to start ngrok tunnel')
    
    def check_ngrok_installed(self):
        """Check if ngrok is installed"""
        try:
            subprocess.run(['ngrok', 'version'], 
                         capture_output=True, check=True)
            return True
        except (subprocess.CalledProcessError, FileNotFoundError):
            self.stdout.write(
                self.style.ERROR('❌ Ngrok is not installed or not in PATH')
            )
            self.stdout.write('Install instructions:')
            self.stdout.write('1. Go to https://ngrok.com/download')
            self.stdout.write('2. Download and add to PATH')
            self.stdout.write('3. Get auth token from https://dashboard.ngrok.com/')
            self.stdout.write('4. Run: ngrok config add-authtoken YOUR_TOKEN')
            return False
    
    def configure_auth_token(self):
        """Configure ngrok auth token"""
        try:
            subprocess.run(['ngrok', 'config', 'add-authtoken', self.auth_token],
                         check=True, capture_output=True)
            self.stdout.write(
                self.style.SUCCESS('✅ Auth token configured')
            )
        except subprocess.CalledProcessError as e:
            raise CommandError(f'Failed to configure auth token: {e}')
    
    def find_existing_tunnel(self):
        """Find existing ngrok tunnel"""
        try:
            response = requests.get('http://localhost:4040/api/tunnels', timeout=5)
            if response.status_code == 200:
                tunnels = response.json().get('tunnels', [])
                for tunnel in tunnels:
                    config = tunnel.get('config', {})
                    if config.get('addr') == f'http://localhost:{self.port}':
                        return tunnel
        except requests.exceptions.RequestException:
            pass
        return None
    
    def handle_existing_tunnel(self, tunnel):
        """Handle existing tunnel"""
        public_url = tunnel['public_url']
        domain = public_url.replace('https://', '').replace('http://', '')
        
        self.stdout.write(
            self.style.WARNING(f'✅ Found existing tunnel: {public_url}')
        )
        
        self.update_django_config({
            'public_url': public_url,
            'domain': domain
        })
        
        self.show_success_message({
            'public_url': public_url,
            'domain': domain
        })
    
    def start_tunnel(self):
        """Start ngrok tunnel"""
        cmd = ['ngrok', 'http', str(self.port), '--region', self.region]
        
        if self.subdomain:
            cmd.extend(['--subdomain', self.subdomain])
        
        self.stdout.write('🚀 Starting ngrok tunnel...')
        
        # Start ngrok process
        process = subprocess.Popen(cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE)
        
        # Wait for ngrok to start
        time.sleep(3)
        
        # Check if process is running
        if process.poll() is not None:
            stdout, stderr = process.communicate()
            self.stdout.write(
                self.style.ERROR(f'❌ Ngrok failed to start: {stderr.decode()}')
            )
            return None
        
        # Get tunnel info
        tunnel = self.find_existing_tunnel()
        if tunnel:
            public_url = tunnel['public_url']
            domain = public_url.replace('https://', '').replace('http://', '')
            return {
                'public_url': public_url,
                'domain': domain,
                'process': process
            }
        
        return None
    
    def update_django_config(self, tunnel_info):
        """Update Django configuration"""
        domain = tunnel_info['domain']
        
        # Set environment variables
        os.environ['NGROK_DOMAIN'] = domain
        os.environ['NGROK_ENABLED'] = 'true'
        
        # Add to ALLOWED_HOSTS if not already there
        if hasattr(settings, 'ALLOWED_HOSTS') and domain not in settings.ALLOWED_HOSTS:
            settings.ALLOWED_HOSTS.append(domain)
        
        self.stdout.write(
            self.style.SUCCESS(f'✅ Added {domain} to ALLOWED_HOSTS')
        )
    
    def show_success_message(self, tunnel_info):
        """Show success message with instructions"""
        public_url = tunnel_info['public_url']
        domain = tunnel_info['domain']
        
        self.stdout.write('')
        self.stdout.write(self.style.SUCCESS('=' * 60))
        self.stdout.write(self.style.SUCCESS('🎉 NGROK TUNNEL IS READY!'))
        self.stdout.write(self.style.SUCCESS('=' * 60))
        self.stdout.write(f'🌐 Your Django app is accessible at:')
        self.stdout.write(f'   {public_url}')
        self.stdout.write('')
        self.stdout.write('📱 Test URLs:')
        self.stdout.write(f'   Homepage: {public_url}/')
        self.stdout.write(f'   Admin: {public_url}/admin/')
        self.stdout.write(f'   Resume Parser: {public_url}/analyse-pdfs/')
        self.stdout.write('')
        self.stdout.write('⚠️  Important Notes:')
        self.stdout.write('   - Keep ngrok running to maintain the tunnel')
        self.stdout.write('   - Free ngrok URLs change on restart')
        self.stdout.write('   - Test webhooks and external APIs now work!')
        self.stdout.write('')
        self.stdout.write('🛑 To stop ngrok: Run "pkill ngrok" or close ngrok terminal')
        self.stdout.write(self.style.SUCCESS('=' * 60))
