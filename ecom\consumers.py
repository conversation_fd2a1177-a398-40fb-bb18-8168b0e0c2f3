import json
from channels.generic.websocket import WebsocketConsumer
from . import models
from django.conf import settings
from .ResumeParser.main import process_resumes
from django.core.cache import cache
import threading
from channels.exceptions import DenyConnection
from channels.layers import get_channel_layer
from asgiref.sync import async_to_sync

class ResumeAnalysisConsumer(WebsocketConsumer):

    def connect(self):
        
        self.user = self.scope["user"]
    
        if not self.user.is_authenticated or not self.user.is_superuser:
            raise DenyConnection("Admin access required")
        
        job_code = self.scope.get("url_route", {}).get("kwargs", {}).get("job_code")
        # print("job_code: " , job_code)
        
        if job_code:
            async_to_sync(self.channel_layer.group_add)(f"job_{job_code}" , self.channel_name)

        
        
        self.accept()
        print(f"Websocket Connected {job_code}")
        # Send current analysis status if any
        # self.send_current_analysis_status()
        # self.send(text_data=json.dumps({"test123" : 123}))

    def disconnect(self, close_code):
        print(f"Websocket disconnected {close_code}")

    # def send_current_analysis_status(self):
    #     """Check if there's an ongoing analysis and send status"""
    #     try:
    #         # Check for ongoing analysis in cache
    #         ongoing_analysis = cache.get('ongoing_analysis')
    #         if ongoing_analysis:
    #             job_code = ongoing_analysis.get('job_code')
    #             progress = ongoing_analysis.get('progress', {})

    #             # Send reconnection message
    #             self.send(text_data=json.dumps({
    #                 'reconnected_to_analysis': True,
    #                 'job_code': job_code,
    #                 'progress': progress,
    #                 'message': f'Reconnected to ongoing analysis for job {job_code}'
    #             }))
    #     except Exception as e:
    #         print(f"Error checking ongoing analysis: {e}")

    def receive(self, text_data):

        data = json.loads(text_data)
        # print("data:",data)     # data: {'start_analysis': True}

        # Handle status check request
        if data.get('check_status'):
            # self.send_current_analysis_status()
            return

        if data.get('start_analysis' , None) and data.get('job_code'):
            
            resume_batches = []
            batch_size = settings.RESUME_BATCH_SIZE
            
            job_code = data.get('job_code' , '')
            job_id = data.get('job_id' , None)
            
            # Find all resume files
            resume_files = []
            
            # Get Resume Files from Database for specific job code
            job_applicants = models.ApplyJob.objects.filter(
                analysed=models.AnalysedChoices.UNANALYZED,
                job_code=job_code
            )
            print(f"Found {job_applicants.count()} unanalyzed resumes for job code: {job_code}")

            for job_applicant in job_applicants:
                print("job_applicant.resume: ",job_applicant.resume)
                file_path = settings.PROJECT_ROOT / str(job_applicant.resume)
                resume_files.append(str(file_path))  # Convert Path to string
            
            
            for i in range(0, len(resume_files), batch_size):
                batch = resume_files[i : i + batch_size]
                resume_batches.append(batch)
                print(f"Created a batch with {len(batch)} resumes.")
                # print(i)

            # print("resume_batches: " , resume_batches)
            
            # Store analysis info in cache
            cache.set('ongoing_analysis', {
                'job_code': job_code,
                'job_id': job_id,
                'total_batches': len(resume_batches),
                'progress': {'completed_batches': 0, 'total_batches': len(resume_batches)}
            }, timeout=3600)  # 1 hour timeout

            self.analyse_resume_batch(job_id, job_code, resume_batches)
                # Clear ongoing analysis from cache
                # cache.delete('ongoing_analysis')
                # res = {
                #     'analysis_completed': True
                # }
                # self.send(text_data=json.dumps(res))
            
    
    def analyse_resume_batch(self , job_id, job_code, resume_batches):
        # Start batch processing in a separate thread
        thread = threading.Thread(target=self._process_batches_threaded, args=(job_id,job_code, resume_batches))
        thread.daemon = True  # Worker thread will die when main thread dies
        thread.start()
        
        # self.send(text_data=json.dumps({"analysis_started" : True}))
        async_to_sync(get_channel_layer().group_send)(
            f"job_{job_code}",
            {
                "type": "group_notification",
                "message": json.dumps({"analysis_started": True})
            }
        )

    def _process_batches_threaded(self, job_id,job_code, resume_batches):
        """Process batches in a separate thread"""
        try:
            for id , batch in enumerate(resume_batches):
                # print(id , type(batch) , len(batch))
                responseData = self.analyse_resumes(job_id, batch , id+1)
                # self.send(text_data=json.dumps(responseData))
                
                async_to_sync(get_channel_layer().group_send)(
                    f"job_{job_code}",
                    {
                        "type": "group_notification",
                        "message": json.dumps(responseData)
                    }
                )

                # Update progress in cache
                try:
                    ongoing_analysis = cache.get('ongoing_analysis')
                    if ongoing_analysis:
                        ongoing_analysis['progress']['completed_batches'] = id + 1
                        cache.set('ongoing_analysis', ongoing_analysis, timeout=3600)
                except Exception as e:
                    print(f"Error updating progress: {e}")

            # Send completion message
            # self.send(text_data=json.dumps({
            #     'analysis_completed': True,
            #     'message': 'All batches processed successfully'
            # }))
            
            async_to_sync(get_channel_layer().group_send)(
                f"job_{job_code}",
                {
                    "type": "group_notification",
                    "message": json.dumps({
                                    'analysis_completed': True,
                                    'message': 'All batches processed successfully'
                                })
                }
            )

            # Clear ongoing analysis from cache
            cache.delete('ongoing_analysis')

        except Exception as e:
            # Send error message if something goes wrong
            # self.send(text_data=json.dumps({
            #     'error': True,
            #     'message': f'Analysis failed: {str(e)}'
            # }))
            
            async_to_sync(get_channel_layer().group_send)(
                f"job_{job_code}",
                {
                    "type": "group_notification",
                    "message": json.dumps({
                                    'error': True,
                                    'message': f'Analysis failed: {str(e)}'
                                })
                }
            )
            
            # Clear ongoing analysis from cache on error
            cache.delete('ongoing_analysis')
            
            
            
    def analyse_resumes(self , job_id , resume_files , batch_no):

        responseData = dict()

        job = models.CreateJob.objects.get(id=job_id)
        job_description = job.job_description
        results = process_resumes(job_description , job.job_code , resume_files=resume_files)
        # print("results: ",results)

        # Get total batches from cache
        ongoing_analysis = cache.get('ongoing_analysis')
        total_batches = ongoing_analysis.get('total_batches', 0) if ongoing_analysis else 0

        responseData["job_code"] = job.job_code
        responseData["results"] = results
        responseData["batch_no"] = batch_no
        responseData["total_batches"] = total_batches
        # responseData["analyseSuccess"] = True
        responseData["batch_analyse_success"] = True

        return responseData
    
    def group_notification(self, event):
        message = event["message"]
        self.send(text_data=message)
        
