#!/usr/bin/env python3
"""
Complete Ngrok Setup for Django E-commerce Project
This script handles everything: installation check, configuration, and startup
"""

import os
import sys
import json
import time
import subprocess
import platform
import urllib.request
from pathlib import Path

class CompleteNgrokSetup:
    def __init__(self):
        self.project_root = Path(__file__).parent
        self.system = platform.system().lower()
        self.django_port = 8000
        
    def print_header(self):
        """Print setup header"""
        print("=" * 60)
        print("🚀 COMPLETE NGROK SETUP FOR DJANGO E-COMMERCE")
        print("=" * 60)
        print(f"System: {platform.system()} {platform.release()}")
        print(f"Python: {sys.version.split()[0]}")
        print(f"Project: {self.project_root}")
        print("-" * 60)
    
    def check_internet(self):
        """Check internet connectivity"""
        try:
            urllib.request.urlopen('https://www.google.com', timeout=5)
            print("✅ Internet connection available")
            return True
        except:
            print("❌ No internet connection")
            return False
    
    def check_ngrok_installed(self):
        """Check if ngrok is installed"""
        try:
            result = subprocess.run(['ngrok', 'version'], 
                                  capture_output=True, text=True, check=True)
            version = result.stdout.strip()
            print(f"✅ Ngrok installed: {version}")
            return True
        except:
            print("❌ Ngrok not installed")
            return False
    
    def install_ngrok_instructions(self):
        """Provide ngrok installation instructions"""
        print("\n📋 NGROK INSTALLATION INSTRUCTIONS")
        print("-" * 40)
        
        if self.system == 'windows':
            print("Option 1 - Chocolatey (Recommended):")
            print("  choco install ngrok")
            print("\nOption 2 - Manual:")
            print("  1. Go to https://ngrok.com/download")
            print("  2. Download ngrok.exe")
            print("  3. Add to PATH or place in project folder")
        
        elif self.system == 'darwin':  # macOS
            print("Option 1 - Homebrew (Recommended):")
            print("  brew install ngrok/ngrok/ngrok")
            print("\nOption 2 - Manual:")
            print("  1. Go to https://ngrok.com/download")
            print("  2. Download and extract ngrok")
            print("  3. Move to /usr/local/bin/")
        
        else:  # Linux
            print("Option 1 - Snap (Recommended):")
            print("  sudo snap install ngrok")
            print("\nOption 2 - Manual:")
            print("  1. Go to https://ngrok.com/download")
            print("  2. Download and extract ngrok")
            print("  3. Move to /usr/local/bin/")
        
        print("\n🔑 After installation:")
        print("1. Sign up at https://ngrok.com/")
        print("2. Get auth token from https://dashboard.ngrok.com/")
        print("3. Run: ngrok config add-authtoken YOUR_TOKEN")
    
    def check_ngrok_auth(self):
        """Check ngrok authentication"""
        print("🔐 Checking ngrok authentication...")
        
        # Try to get auth token from config
        try:
            if self.system == 'windows':
                config_path = Path.home() / 'AppData' / 'Local' / 'ngrok' / 'ngrok.yml'
            else:
                config_path = Path.home() / '.config' / 'ngrok' / 'ngrok.yml'
            
            if config_path.exists():
                with open(config_path, 'r') as f:
                    content = f.read()
                    if 'authtoken:' in content:
                        print("✅ Ngrok auth token found")
                        return True
        except:
            pass
        
        print("❌ Ngrok auth token not configured")
        print("\n🔑 To configure authentication:")
        print("1. Sign up at https://ngrok.com/")
        print("2. Get your auth token from https://dashboard.ngrok.com/")
        print("3. Run: ngrok config add-authtoken YOUR_TOKEN")
        return False
    
    def check_django_setup(self):
        """Check Django setup"""
        print("🔧 Checking Django setup...")
        
        # Check if manage.py exists
        manage_py = self.project_root / 'manage.py'
        if not manage_py.exists():
            print("❌ manage.py not found")
            return False
        
        # Check if Django is installed
        try:
            subprocess.run([sys.executable, '-c', 'import django'], 
                         check=True, capture_output=True)
            print("✅ Django is available")
        except:
            print("❌ Django not installed")
            print("Run: pip install django")
            return False
        
        # Check settings
        settings_file = self.project_root / 'ecommerce' / 'settings.py'
        if not settings_file.exists():
            print("❌ Django settings.py not found")
            return False
        
        print("✅ Django setup looks good")
        return True
    
    def start_ngrok_tunnel(self):
        """Start ngrok tunnel"""
        print(f"🌐 Starting ngrok tunnel on port {self.django_port}...")
        
        # Start ngrok
        cmd = ['ngrok', 'http', str(self.django_port), '--log=stdout']
        process = subprocess.Popen(cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE)
        
        # Wait for startup
        time.sleep(3)
        
        # Check if running
        if process.poll() is not None:
            stdout, stderr = process.communicate()
            print(f"❌ Ngrok failed to start:")
            print(stderr.decode())
            return None, None
        
        # Get tunnel info
        try:
            import requests
            response = requests.get('http://localhost:4040/api/tunnels', timeout=5)
            if response.status_code == 200:
                tunnels = response.json().get('tunnels', [])
                for tunnel in tunnels:
                    if tunnel.get('config', {}).get('addr') == f'http://localhost:{self.django_port}':
                        public_url = tunnel['public_url']
                        domain = public_url.replace('https://', '').replace('http://', '')
                        return public_url, domain
        except Exception as e:
            print(f"⚠️  Could not get tunnel info: {e}")
        
        return None, None
    
    def create_env_file(self, domain):
        """Create .env file with ngrok configuration"""
        env_file = self.project_root / '.env'
        
        env_content = f"""# Ngrok Configuration (Auto-generated)
NGROK_ENABLED=true
NGROK_DOMAIN={domain}

# Django Development Settings
DEBUG=True
"""
        
        with open(env_file, 'w') as f:
            f.write(env_content)
        
        print(f"✅ Created .env file with ngrok configuration")
    
    def show_final_instructions(self, public_url, domain):
        """Show final usage instructions"""
        print("\n" + "=" * 60)
        print("🎉 NGROK SETUP COMPLETE!")
        print("=" * 60)
        print(f"🌐 Your Django app is now accessible at:")
        print(f"   {public_url}")
        print(f"\n📱 Test these URLs:")
        print(f"   Homepage: {public_url}/")
        print(f"   Admin Panel: {public_url}/admin/")
        print(f"   Resume Parser: {public_url}/analyse-pdfs/")
        print(f"   Face Login: {public_url}/face-login/")
        print(f"   Cart: {public_url}/cart/")
        print(f"\n🔧 Development URLs:")
        print(f"   Local Django: http://localhost:{self.django_port}/")
        print(f"   Ngrok Dashboard: http://localhost:4040/")
        print(f"\n💡 Perfect for testing:")
        print(f"   ✓ Payment webhooks (Stripe, PhonePe, PayPal)")
        print(f"   ✓ External API integrations")
        print(f"   ✓ Mobile app testing")
        print(f"   ✓ Client demos")
        print(f"   ✓ WebSocket connections")
        print(f"\n⚠️  Important notes:")
        print(f"   - Keep ngrok running to maintain tunnel")
        print(f"   - Free ngrok URLs change on restart")
        print(f"   - Domain automatically added to ALLOWED_HOSTS")
        print(f"   - Environment variables configured in .env")
        print(f"\n🚀 Next steps:")
        print(f"   1. Start Django: python manage.py runserver")
        print(f"   2. Test your public URL: {public_url}")
        print(f"   3. Configure webhooks with your public URL")
        print(f"\n🛑 To stop ngrok: Press Ctrl+C or run 'pkill ngrok'")
        print("=" * 60)
    
    def run_complete_setup(self):
        """Run the complete setup process"""
        self.print_header()
        
        # Step 1: Check internet
        if not self.check_internet():
            print("❌ Internet connection required for ngrok")
            return 1
        
        # Step 2: Check ngrok installation
        if not self.check_ngrok_installed():
            self.install_ngrok_instructions()
            return 1
        
        # Step 3: Check ngrok authentication
        if not self.check_ngrok_auth():
            return 1
        
        # Step 4: Check Django setup
        if not self.check_django_setup():
            return 1
        
        # Step 5: Start ngrok tunnel
        public_url, domain = self.start_ngrok_tunnel()
        if not public_url:
            print("❌ Failed to start ngrok tunnel")
            return 1
        
        # Step 6: Create configuration
        self.create_env_file(domain)
        
        # Step 7: Show final instructions
        self.show_final_instructions(public_url, domain)
        
        # Step 8: Keep tunnel alive
        try:
            print("\n⏳ Keeping tunnel alive... (Press Ctrl+C to stop)")
            while True:
                time.sleep(10)
                # Could add health checks here
        except KeyboardInterrupt:
            print("\n🛑 Stopping ngrok tunnel...")
        
        return 0

def main():
    """Main entry point"""
    setup = CompleteNgrokSetup()
    return setup.run_complete_setup()

if __name__ == "__main__":
    sys.exit(main())
